import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { EinvoiceService } from '@/services/EinvoiceService'
import { EinvoiceStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('company')
    const status = searchParams.getAll('status') as EinvoiceStatus[]
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const search = searchParams.get('search')
    const createdById = searchParams.get('createdBy')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const { prisma } = await import('@/lib/prisma')
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this company' },
          { status: 403 }
        )
      }
    }

    const filters = {
      companyId,
      status: status.length > 0 ? status : undefined,
      dateFrom: dateFrom ? new Date(dateFrom) : undefined,
      dateTo: dateTo ? new Date(dateTo) : undefined,
      search: search || undefined,
      createdById: createdById || undefined,
      limit,
      offset,
    }

    const result = await EinvoiceService.getInvoices(filters)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      companyId,
      invoiceNumber,
      issueDate,
      dueDate,
      currency,
      billFromName,
      billFromAddress,
      billFromEmail,
      billFromPhone,
      billToName,
      billToAddress,
      billToEmail,
      billToPhone,
      description,
      notes,
      terms,
      items,
      metadata,
    } = body

    // Validate required fields
    if (!companyId || !issueDate || !billFromName || !billToName || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const { prisma } = await import('@/lib/prisma')
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this company' },
          { status: 403 }
        )
      }
    }

    const invoiceData = {
      companyId,
      createdById: session.user.id,
      invoiceNumber,
      issueDate: new Date(issueDate),
      dueDate: dueDate ? new Date(dueDate) : undefined,
      currency,
      billFromName,
      billFromAddress,
      billFromEmail,
      billFromPhone,
      billToName,
      billToAddress,
      billToEmail,
      billToPhone,
      description,
      notes,
      terms,
      items,
      metadata,
    }

    const invoice = await EinvoiceService.createInvoice(invoiceData)

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    console.error('Error creating invoice:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
