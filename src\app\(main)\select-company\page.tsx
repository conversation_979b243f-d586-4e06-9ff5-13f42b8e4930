'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface Company {
  id: string
  name: string
  status: string
  environment: string
  subscriptionEnd: string | null
  country: {
    name: string
    code: string
  }
}

export default function SelectCompanyPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const countryId = searchParams.get('country')
  
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCompany, setSelectedCompany] = useState<string>('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
      return
    }

    if (!countryId) {
      router.push('/select-country')
      return
    }

    if (status === 'authenticated') {
      fetchCompanies()
    }
  }, [status, router, countryId])

  const fetchCompanies = async () => {
    try {
      const response = await fetch(`/api/companies?country=${countryId}`)
      if (response.ok) {
        const data = await response.json()
        setCompanies(data)
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCompanySelect = (companyId: string) => {
    setSelectedCompany(companyId)
  }

  const handleContinue = () => {
    if (selectedCompany) {
      // Store selected company in session storage or context
      sessionStorage.setItem('selectedCompany', selectedCompany)
      router.push('/dashboard')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800'
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getEnvironmentColor = (environment: string) => {
    return environment === 'production' 
      ? 'bg-blue-100 text-blue-800' 
      : 'bg-yellow-100 text-yellow-800'
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-extrabold text-gray-900">
            Select Company
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Choose the company you want to work with
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Available Companies</CardTitle>
            <CardDescription>
              Select a company to access your dashboard and manage eInvoicing
            </CardDescription>
          </CardHeader>
          <CardContent>
            {companies.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No companies available for this country</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => router.push('/select-country')}
                >
                  Back to Country Selection
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {companies.map((company) => (
                  <div
                    key={company.id}
                    className={`p-6 border rounded-lg cursor-pointer transition-colors ${
                      selectedCompany === company.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleCompanySelect(company.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">
                              {company.name}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {company.country.name} ({company.country.code})
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex items-center space-x-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                            {company.status}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEnvironmentColor(company.environment)}`}>
                            {company.environment}
                          </span>
                          {company.subscriptionEnd && (
                            <span className="text-xs text-gray-500">
                              Subscription ends: {new Date(company.subscriptionEnd).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {selectedCompany === company.id && (
                        <div className="ml-4">
                          <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedCompany && (
              <div className="mt-6 flex justify-between">
                <Button 
                  variant="outline"
                  onClick={() => router.push('/select-country')}
                >
                  Back
                </Button>
                <Button onClick={handleContinue}>
                  Continue to Dashboard
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
