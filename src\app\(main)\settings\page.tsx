'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { NotificationBell } from '@/components/layout/NotificationBell'

interface VersionInfo {
  current: string
  latest: string
  releaseDate: string
  changelog: string[]
}

interface DeveloperSettings {
  debugMode: boolean
  apiLogging: boolean
  performanceMetrics: boolean
  experimentalFeatures: boolean
}

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [versionInfo, setVersionInfo] = useState<VersionInfo>({
    current: '2025.05.3',
    latest: '2025.05.3',
    releaseDate: '2025-05-28',
    changelog: [
      'Enhanced login page with progressive authentication',
      'Added version control and developer settings',
      'Improved user management interface',
      'Fixed authentication middleware compatibility'
    ]
  })
  const [developerSettings, setDeveloperSettings] = useState<DeveloperSettings>({
    debugMode: false,
    apiLogging: false,
    performanceMetrics: false,
    experimentalFeatures: false
  })

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
    }
  }, [status, router])

  useEffect(() => {
    // Load developer settings from localStorage
    const savedSettings = localStorage.getItem('developerSettings')
    if (savedSettings) {
      setDeveloperSettings(JSON.parse(savedSettings))
    }
  }, [])

  const handleSettingChange = (setting: keyof DeveloperSettings) => {
    const newSettings = {
      ...developerSettings,
      [setting]: !developerSettings[setting]
    }
    setDeveloperSettings(newSettings)
    localStorage.setItem('developerSettings', JSON.stringify(newSettings))
  }

  const checkForUpdates = async () => {
    setLoading(true)
    // Simulate API call to check for updates
    setTimeout(() => {
      setVersionInfo(prev => ({
        ...prev,
        latest: '2025.05.4',
        changelog: [
          'New dashboard widgets',
          'Enhanced notification system',
          'Performance improvements',
          'Bug fixes and security updates'
        ]
      }))
      setLoading(false)
    }, 2000)
  }

  const exportLogs = () => {
    // Export application logs
    const logs = {
      timestamp: new Date().toISOString(),
      version: versionInfo.current,
      settings: developerSettings,
      userAgent: navigator.userAgent,
      sessionInfo: {
        userId: session?.user?.id,
        role: session?.user?.role
      }
    }
    
    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `pinnacle-logs-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
              <p className="text-sm text-gray-600">
                Manage your account and developer preferences
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <NotificationBell />
              <Button 
                variant="outline" 
                onClick={() => router.push('/dashboard')}
              >
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Version Control */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                </svg>
                Version Control
              </CardTitle>
              <CardDescription>
                Current version and update information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Current Version:</span>
                <span className="text-sm text-gray-600">{versionInfo.current}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Latest Version:</span>
                <span className="text-sm text-gray-600">{versionInfo.latest}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Release Date:</span>
                <span className="text-sm text-gray-600">{versionInfo.releaseDate}</span>
              </div>
              
              {versionInfo.current !== versionInfo.latest && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <p className="text-sm text-blue-800 font-medium">Update Available!</p>
                  <p className="text-xs text-blue-600">Version {versionInfo.latest} is now available.</p>
                </div>
              )}

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Recent Changes:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {versionInfo.changelog.map((change, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {change}
                    </li>
                  ))}
                </ul>
              </div>

              <Button 
                onClick={checkForUpdates} 
                disabled={loading}
                className="w-full"
              >
                {loading ? 'Checking...' : 'Check for Updates'}
              </Button>
            </CardContent>
          </Card>

          {/* Developer Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                Developer Settings
              </CardTitle>
              <CardDescription>
                Advanced settings for developers and power users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(developerSettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <p className="text-xs text-gray-500">
                      {key === 'debugMode' && 'Enable detailed debugging information'}
                      {key === 'apiLogging' && 'Log all API requests and responses'}
                      {key === 'performanceMetrics' && 'Show performance metrics in console'}
                      {key === 'experimentalFeatures' && 'Enable experimental features'}
                    </p>
                  </div>
                  <button
                    onClick={() => handleSettingChange(key as keyof DeveloperSettings)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      value ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        value ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}

              <div className="pt-4 border-t">
                <Button 
                  onClick={exportLogs}
                  variant="outline"
                  className="w-full"
                >
                  Export Application Logs
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
