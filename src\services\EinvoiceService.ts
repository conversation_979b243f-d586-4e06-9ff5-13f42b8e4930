import { prisma } from '@/lib/prisma'
import { NotificationService } from './NotificationService'
import {
  EinvoiceStatus,
  ActionType,
  ActorType,
  NotificationType,
  NotificationPriority
} from '@prisma/client'

export interface CreateEinvoiceData {
  companyId: string
  createdById: string

  // Basic Information
  invoiceNumber?: string // Auto-generated if not provided
  issueDate: Date
  dueDate?: Date
  currency?: string

  // Parties
  billFromName: string
  billFromAddress: any
  billFromEmail?: string
  billFromPhone?: string

  billToName: string
  billToAddress: any
  billToEmail?: string
  billToPhone?: string

  // Additional Information
  description?: string
  notes?: string
  terms?: string

  // Items
  items: CreateEinvoiceItemData[]

  // Metadata
  metadata?: any
}

export interface CreateEinvoiceItemData {
  description: string
  quantity: number
  unitPrice: number
  discount?: number
  taxRate?: number
  productCode?: string
  unit?: string
  metadata?: any
}

export interface UpdateEinvoiceData extends Partial<CreateEinvoiceData> {
  id: string
}

export interface EinvoiceFilters {
  companyId: string
  status?: EinvoiceStatus[]
  dateFrom?: Date
  dateTo?: Date
  search?: string
  createdById?: string
  limit?: number
  offset?: number
}

export class EinvoiceService {
  /**
   * Generate next invoice number for a company
   */
  static async generateInvoiceNumber(companyId: string): Promise<string> {
    const currentYear = new Date().getFullYear()
    const prefix = `INV-${currentYear}-`

    // Find the highest invoice number for this year
    const lastInvoice = await prisma.einvoice.findFirst({
      where: {
        companyId,
        invoiceNumber: {
          startsWith: prefix,
        },
      },
      orderBy: {
        invoiceNumber: 'desc',
      },
    })

    let nextNumber = 1
    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoiceNumber.replace(prefix, ''))
      nextNumber = lastNumber + 1
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`
  }

  /**
   * Calculate invoice totals from items
   */
  static calculateTotals(items: CreateEinvoiceItemData[]) {
    let subtotal = 0
    let taxAmount = 0
    let discountAmount = 0

    const calculatedItems = items.map(item => {
      const lineSubtotal = item.quantity * item.unitPrice
      const lineDiscount = item.discount || 0
      const lineAfterDiscount = lineSubtotal - lineDiscount
      const lineTax = lineAfterDiscount * (item.taxRate || 0) / 100
      const lineTotal = lineAfterDiscount + lineTax

      subtotal += lineSubtotal
      discountAmount += lineDiscount
      taxAmount += lineTax

      return {
        ...item,
        lineTotal,
      }
    })

    const totalAmount = subtotal - discountAmount + taxAmount

    return {
      items: calculatedItems,
      subtotal,
      taxAmount,
      discountAmount,
      totalAmount,
    }
  }

  /**
   * Create a new invoice
   */
  static async createInvoice(data: CreateEinvoiceData) {
    try {
      // Generate invoice number if not provided
      const invoiceNumber = data.invoiceNumber ||
        await this.generateInvoiceNumber(data.companyId)

      // Calculate totals
      const { items, subtotal, taxAmount, discountAmount, totalAmount } =
        this.calculateTotals(data.items)

      // Create invoice with items in a transaction
      const invoice = await prisma.$transaction(async (tx) => {
        // Create the invoice
        const newInvoice = await tx.einvoice.create({
          data: {
            companyId: data.companyId,
            createdById: data.createdById,
            invoiceNumber,
            issueDate: data.issueDate,
            dueDate: data.dueDate,
            currency: data.currency || 'USD',

            subtotal,
            taxAmount,
            discountAmount,
            totalAmount,

            billFromName: data.billFromName,
            billFromAddress: data.billFromAddress,
            billFromEmail: data.billFromEmail,
            billFromPhone: data.billFromPhone,

            billToName: data.billToName,
            billToAddress: data.billToAddress,
            billToEmail: data.billToEmail,
            billToPhone: data.billToPhone,

            description: data.description,
            notes: data.notes,
            terms: data.terms,
            metadata: data.metadata,
          },
        })

        // Create invoice items
        await tx.einvoiceItem.createMany({
          data: items.map(item => ({
            einvoiceId: newInvoice.id,
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount || 0,
            taxRate: item.taxRate || 0,
            lineTotal: item.lineTotal,
            productCode: item.productCode,
            unit: item.unit,
            metadata: item.metadata,
          })),
        })

        // Create initial history entry
        await tx.einvoiceHistory.create({
          data: {
            einvoiceId: newInvoice.id,
            toStatus: EinvoiceStatus.DRAFT,
            changedById: data.createdById,
            reason: 'Invoice created',
          },
        })

        return newInvoice
      })

      // Log activity and create notifications
      await NotificationService.logActivityAndNotify(
        {
          userId: data.createdById,
          actorType: ActorType.USER,
          actorId: data.createdById,
          companyId: data.companyId,
          actionType: ActionType.EINVOICE_CREATED,
          targetType: 'Einvoice',
          targetId: invoice.id,
          details: {
            invoiceNumber,
            totalAmount,
            currency: data.currency || 'USD',
          },
        },
        [data.createdById] // Notify the creator
      )

      return await this.getInvoiceById(invoice.id)
    } catch (error) {
      console.error('Failed to create invoice:', error)
      throw error
    }
  }

  /**
   * Update an existing invoice
   */
  static async updateInvoice(data: UpdateEinvoiceData, updatedById: string) {
    try {
      const { id, items, ...invoiceData } = data

      // Get current invoice
      const currentInvoice = await prisma.einvoice.findUnique({
        where: { id },
        include: { items: true },
      })

      if (!currentInvoice) {
        throw new Error('Invoice not found')
      }

      // Check if invoice can be updated
      if (currentInvoice.status === EinvoiceStatus.SENT ||
          currentInvoice.status === EinvoiceStatus.PAID) {
        throw new Error('Cannot update sent or paid invoices')
      }

      let calculatedData = {}
      if (items) {
        const { items: calculatedItems, subtotal, taxAmount, discountAmount, totalAmount } =
          this.calculateTotals(items)
        calculatedData = { subtotal, taxAmount, discountAmount, totalAmount }
      }

      // Update invoice in transaction
      const updatedInvoice = await prisma.$transaction(async (tx) => {
        // Update the invoice
        const updated = await tx.einvoice.update({
          where: { id },
          data: {
            ...invoiceData,
            ...calculatedData,
          },
        })

        // Update items if provided
        if (items) {
          // Delete existing items
          await tx.einvoiceItem.deleteMany({
            where: { einvoiceId: id },
          })

          // Create new items
          const { items: calculatedItems } = this.calculateTotals(items)
          await tx.einvoiceItem.createMany({
            data: calculatedItems.map(item => ({
              einvoiceId: id,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              discount: item.discount || 0,
              taxRate: item.taxRate || 0,
              lineTotal: item.lineTotal,
              productCode: item.productCode,
              unit: item.unit,
              metadata: item.metadata,
            })),
          })
        }

        // Create history entry
        await tx.einvoiceHistory.create({
          data: {
            einvoiceId: id,
            fromStatus: currentInvoice.status,
            toStatus: currentInvoice.status, // Status unchanged
            changedById: updatedById,
            reason: 'Invoice updated',
          },
        })

        return updated
      })

      // Log activity
      await NotificationService.logActivityAndNotify(
        {
          userId: updatedById,
          actorType: ActorType.USER,
          actorId: updatedById,
          companyId: currentInvoice.companyId,
          actionType: ActionType.EINVOICE_UPDATED,
          targetType: 'Einvoice',
          targetId: id,
          details: {
            invoiceNumber: currentInvoice.invoiceNumber,
            changes: Object.keys(invoiceData),
          },
        },
        [updatedById]
      )

      return await this.getInvoiceById(id)
    } catch (error) {
      console.error('Failed to update invoice:', error)
      throw error
    }
  }

  /**
   * Update invoice status
   */
  static async updateInvoiceStatus(
    invoiceId: string,
    newStatus: EinvoiceStatus,
    changedById: string,
    reason?: string,
    notes?: string
  ) {
    try {
      const currentInvoice = await prisma.einvoice.findUnique({
        where: { id: invoiceId },
      })

      if (!currentInvoice) {
        throw new Error('Invoice not found')
      }

      // Validate status transition
      if (!this.isValidStatusTransition(currentInvoice.status, newStatus)) {
        throw new Error(`Invalid status transition from ${currentInvoice.status} to ${newStatus}`)
      }

      // Update invoice status in transaction
      const updatedInvoice = await prisma.$transaction(async (tx) => {
        // Update the invoice
        const updateData: any = { status: newStatus }

        if (newStatus === EinvoiceStatus.SENT) {
          updateData.sentAt = new Date()
        } else if (newStatus === EinvoiceStatus.PAID) {
          updateData.paidAt = new Date()
        }

        const updated = await tx.einvoice.update({
          where: { id: invoiceId },
          data: updateData,
        })

        // Create history entry
        await tx.einvoiceHistory.create({
          data: {
            einvoiceId,
            fromStatus: currentInvoice.status,
            toStatus: newStatus,
            changedById,
            reason,
            notes,
          },
        })

        return updated
      })

      // Log activity and create notifications
      await NotificationService.logActivityAndNotify(
        {
          userId: changedById,
          actorType: ActorType.USER,
          actorId: changedById,
          companyId: currentInvoice.companyId,
          actionType: ActionType.EINVOICE_UPDATED,
          targetType: 'Einvoice',
          targetId: invoiceId,
          details: {
            invoiceNumber: currentInvoice.invoiceNumber,
            fromStatus: currentInvoice.status,
            toStatus: newStatus,
            reason,
          },
        },
        [currentInvoice.createdById || changedById]
      )

      return await this.getInvoiceById(invoiceId)
    } catch (error) {
      console.error('Failed to update invoice status:', error)
      throw error
    }
  }

  /**
   * Validate status transition
   */
  static isValidStatusTransition(fromStatus: EinvoiceStatus, toStatus: EinvoiceStatus): boolean {
    const validTransitions: Record<EinvoiceStatus, EinvoiceStatus[]> = {
      [EinvoiceStatus.DRAFT]: [
        EinvoiceStatus.PENDING_REVIEW,
        EinvoiceStatus.CANCELLED,
      ],
      [EinvoiceStatus.PENDING_REVIEW]: [
        EinvoiceStatus.APPROVED,
        EinvoiceStatus.DRAFT,
        EinvoiceStatus.CANCELLED,
      ],
      [EinvoiceStatus.APPROVED]: [
        EinvoiceStatus.SENT,
        EinvoiceStatus.CANCELLED,
      ],
      [EinvoiceStatus.SENT]: [
        EinvoiceStatus.VIEWED,
        EinvoiceStatus.PARTIALLY_PAID,
        EinvoiceStatus.PAID,
        EinvoiceStatus.OVERDUE,
        EinvoiceStatus.DISPUTED,
        EinvoiceStatus.VOIDED,
      ],
      [EinvoiceStatus.VIEWED]: [
        EinvoiceStatus.PARTIALLY_PAID,
        EinvoiceStatus.PAID,
        EinvoiceStatus.OVERDUE,
        EinvoiceStatus.DISPUTED,
      ],
      [EinvoiceStatus.PARTIALLY_PAID]: [
        EinvoiceStatus.PAID,
        EinvoiceStatus.OVERDUE,
        EinvoiceStatus.DISPUTED,
      ],
      [EinvoiceStatus.PAID]: [], // Final status
      [EinvoiceStatus.OVERDUE]: [
        EinvoiceStatus.PARTIALLY_PAID,
        EinvoiceStatus.PAID,
        EinvoiceStatus.DISPUTED,
      ],
      [EinvoiceStatus.CANCELLED]: [], // Final status
      [EinvoiceStatus.VOIDED]: [], // Final status
      [EinvoiceStatus.DISPUTED]: [
        EinvoiceStatus.PARTIALLY_PAID,
        EinvoiceStatus.PAID,
        EinvoiceStatus.OVERDUE,
      ],
    }

    return validTransitions[fromStatus]?.includes(toStatus) || false
  }

  /**
   * Get invoice by ID with all related data
   */
  static async getInvoiceById(id: string) {
    return await prisma.einvoice.findUnique({
      where: { id },
      include: {
        company: {
          select: {
            id: true,
            name: true,
            country: {
              select: {
                name: true,
                code: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        history: {
          include: {
            changedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        attachments: {
          include: {
            uploadedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    })
  }
}
