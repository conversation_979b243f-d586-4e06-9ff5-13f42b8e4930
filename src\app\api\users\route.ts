import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges
    if (session.user.role !== 'PORTAL_ADMIN' && session.user.role !== 'COMPANY_ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const company = searchParams.get('company')
    const search = searchParams.get('search')
    const role = searchParams.get('role')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!company) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Build where clause
    const where: any = {}

    // For company admins, only show users from their company
    if (session.user.role === 'COMPANY_ADMIN') {
      where.companyAccess = {
        some: {
          companyId: company
        }
      }
    } else if (session.user.role === 'PORTAL_ADMIN') {
      // Portal admins can see users from the specified company
      where.companyAccess = {
        some: {
          companyId: company
        }
      }
    }

    // Add search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Add role filter
    if (role) {
      where.role = role as UserRole
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          companyAccess: {
            include: {
              company: {
                select: {
                  id: true,
                  name: true,
                  status: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset
      }),
      prisma.user.count({ where })
    ])

    return NextResponse.json({
      users,
      total,
      hasMore: total > offset + limit
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges
    if (session.user.role !== 'PORTAL_ADMIN' && session.user.role !== 'COMPANY_ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { email, password, name, role, companyIds } = body

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Validate role permissions
    const userRole = role as UserRole || UserRole.USER
    if (session.user.role === 'COMPANY_ADMIN' && 
        (userRole === 'SUPER_ADMIN' || userRole === 'PORTAL_ADMIN')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create this role' },
        { status: 403 }
      )
    }

    // Create user with company access
    const user = await prisma.$transaction(async (tx) => {
      const newUser = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          role: userRole
        }
      })

      // Add company access if provided
      if (companyIds && companyIds.length > 0) {
        await tx.userCompanyAccess.createMany({
          data: companyIds.map((companyId: string) => ({
            userId: newUser.id,
            companyId
          }))
        })
      }

      return newUser
    })

    // Return user without password
    const { password: _, ...userWithoutPassword } = user

    return NextResponse.json(userWithoutPassword, { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
