import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { EinvoiceService } from '@/services/EinvoiceService'
import { prisma } from '@/lib/prisma'
import { EinvoiceStatus } from '@prisma/client'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { status, reason, notes } = body

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      )
    }

    // Validate status value
    if (!Object.values(EinvoiceStatus).includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      )
    }

    // Get current invoice to verify access
    const currentInvoice = await prisma.einvoice.findUnique({
      where: { id },
    })

    if (!currentInvoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: currentInvoice.companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this invoice' },
          { status: 403 }
        )
      }
    }

    const updatedInvoice = await EinvoiceService.updateInvoiceStatus(
      id,
      status,
      session.user.id,
      reason,
      notes
    )

    return NextResponse.json(updatedInvoice)
  } catch (error) {
    console.error('Error updating invoice status:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
