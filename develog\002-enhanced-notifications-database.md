# Develog #002 - Enhanced Notification System & Database Structure

**Date:** May 28, 2025
**Status:** 🔄 In Progress  
**Phase:** Enhanced Notifications & Real-time Capabilities

## 🎯 Objectives

### 1. Enhanced Database Schema
- 🔄 Expand notification system with comprehensive tracking
- 🔄 Implement real-time transaction logging
- 🔄 Add Socket.io integration preparation
- 🔄 Enhance user activity logging with metadata

### 2. Real-time Notification System
- 🔄 Socket.io server setup
- 🔄 Client-side real-time connection
- 🔄 Notification broadcasting system
- 🔄 User preference-based notification filtering

### 3. Transaction Logging Enhancement
- 🔄 Comprehensive activity tracking
- 🔄 Metadata-rich logging system
- 🔄 Notification generation from activities
- 🔄 MSSQL integration for detailed internal logs

## 📊 Enhanced Database Schema

### New/Updated Models

#### Enhanced User Model
```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?  // Nullable for OAuth users
  name      String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Enhanced relationships
  companies     UserCompanyAccess[]
  preferences   UserPreference?
  notifications Notification[]
  activityLogs  UserActivityLog[]
  
  // Socket.io session tracking (optional)
  socketSessions SocketSession[]
}
```

#### Enhanced UserRole Enum
```prisma
enum UserRole {
  SUPER_ADMIN    // Platform-wide administration
  PORTAL_ADMIN   // Portal management and impersonation
  COMPANY_ADMIN  // Company-specific administration
  COMPANY_USER   // Regular company user
  USER           // Basic authenticated user
}
```

#### Enhanced Notification System
```prisma
model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  type      NotificationType
  title     String
  message   String
  isRead    Boolean  @default(false)
  readAt    DateTime?
  link      String?  // Deep link for navigation
  
  // Transaction logging integration
  metadata  Json?    // Rich transaction data
  priority  NotificationPriority @default(NORMAL)
  
  // Activity log relationship
  activityLogId String?            @unique
  activityLog   UserActivityLog?   @relation(fields: [activityLogId], references: [id])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([userId, isRead])
  @@index([createdAt])
}
```

#### Enhanced NotificationType Enum
```prisma
enum NotificationType {
  // System notifications
  SYSTEM_ANNOUNCEMENT
  SECURITY_ALERT
  SUBSCRIPTION_REMINDER
  
  // eInvoicing notifications
  EINVOICE_CREATED
  EINVOICE_STATUS_UPDATED
  EINVOICE_PAYMENT_RECEIVED
  EINVOICE_OVERDUE
  
  // User management
  USER_ADDED_TO_COMPANY
  USER_ROLE_CHANGED
  NEW_USER_REGISTERED
  
  // Transaction notifications
  TRANSACTION_COMPLETED
  TRANSACTION_FAILED
  PAYMENT_PROCESSED
  
  // Collaboration
  MENTIONED_IN_COMMENT
  TASK_ASSIGNED
  DOCUMENT_SHARED
}
```

#### New NotificationPriority Enum
```prisma
enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
```

#### Enhanced UserActivityLog
```prisma
model UserActivityLog {
  id          String    @id @default(cuid())
  
  // Actor information
  userId      String?   // User who performed the action
  user        User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  actorType   ActorType // Who/what initiated the action
  actorId     String?   // ID of the actor
  
  // Context information
  companyId   String?   // Company context
  company     Company?  @relation(fields: [companyId], references: [id], onDelete: SetNull)
  
  // Action details
  actionType  ActionType // Standardized action types
  targetType  String?    // Entity type affected
  targetId    String?    // ID of affected entity
  
  // Rich metadata and tracking
  details     Json?      // Specific action data
  metadata    Json?      // Additional context
  status      ActionStatus @default(SUCCESS)
  
  // Security and audit
  ipAddress   String?
  userAgent   String?
  sessionId   String?
  
  // Timestamps
  createdAt   DateTime  @default(now())
  
  // Notification relationship
  notifications Notification[]
  
  @@index([userId, createdAt])
  @@index([companyId, createdAt])
  @@index([actionType, createdAt])
}
```

#### New Enums for Activity Logging
```prisma
enum ActorType {
  USER
  SYSTEM
  API
  WEBHOOK
  SCHEDULED_TASK
}

enum ActionType {
  // Authentication
  USER_LOGIN
  USER_LOGOUT
  USER_LOGIN_FAILED
  PASSWORD_CHANGED
  
  // User management
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  USER_ROLE_CHANGED
  USER_COMPANY_ACCESS_GRANTED
  USER_COMPANY_ACCESS_REVOKED
  
  // eInvoicing
  EINVOICE_CREATED
  EINVOICE_UPDATED
  EINVOICE_SENT
  EINVOICE_VIEWED
  EINVOICE_PAID
  EINVOICE_VOIDED
  
  // Company management
  COMPANY_CREATED
  COMPANY_UPDATED
  COMPANY_SETTINGS_CHANGED
  
  // System events
  SYSTEM_BACKUP_COMPLETED
  SYSTEM_MAINTENANCE_STARTED
  SYSTEM_ERROR_OCCURRED
}

enum ActionStatus {
  SUCCESS
  PENDING
  FAILED
  CANCELLED
}
```

#### Enhanced UserPreference
```prisma
model UserPreference {
  id              String   @id @default(cuid())
  userId          String   @unique
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Notification preferences
  receiveEmailNotifications Boolean @default(true)
  receiveInAppNotifications Boolean @default(true)
  receivePushNotifications  Boolean @default(false)
  
  // Granular notification settings
  notificationSettings Json? // Type-specific preferences
  
  // UI preferences
  theme           String @default("light")
  language        String @default("en")
  timezone        String @default("UTC")
  
  // Dashboard preferences
  defaultDashboard String?
  
  updatedAt       DateTime @updatedAt
}
```

#### Socket.io Session Tracking
```prisma
model SocketSession {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  socketId  String   @unique
  companyId String?  // Current company context
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([userId, isActive])
}
```

## 🔄 Real-time Integration Architecture

### Socket.io Event Flow
1. **Action Trigger** → Backend service performs action
2. **Activity Log** → Create `UserActivityLog` entry
3. **Notification Generation** → Create `Notification` if criteria met
4. **Real-time Broadcast** → Emit to relevant users via Socket.io
5. **Client Update** → Update UI with new notification

### Notification Generation Logic
```typescript
// Example service function
async function createNotificationFromActivity(
  activityLog: UserActivityLog,
  targetUserIds: string[]
) {
  const notifications = await Promise.all(
    targetUserIds.map(userId => 
      prisma.notification.create({
        data: {
          userId,
          type: mapActionToNotificationType(activityLog.actionType),
          title: generateNotificationTitle(activityLog),
          message: generateNotificationMessage(activityLog),
          metadata: activityLog.details,
          activityLogId: activityLog.id,
        }
      })
    )
  );
  
  // Emit real-time notifications
  notifications.forEach(notification => {
    io.to(`user_${notification.userId}`).emit('new_notification', notification);
  });
}
```

## 📝 Implementation Plan

### Phase 1: Database Schema Update
- [ ] Update Prisma schema with enhanced models
- [ ] Create migration for new fields and relationships
- [ ] Update seed script with new data structure
- [ ] Add proper indexes for performance

### Phase 2: Socket.io Integration
- [ ] Install and configure Socket.io server
- [ ] Create Socket.io client wrapper
- [ ] Implement user session management
- [ ] Add real-time notification broadcasting

### Phase 3: Enhanced Logging System
- [ ] Create activity logging service
- [ ] Implement notification generation logic
- [ ] Add MSSQL detailed logging integration
- [ ] Create audit trail interfaces

### Phase 4: UI Enhancements
- [ ] Real-time notification components
- [ ] Activity feed interface
- [ ] Notification preferences panel
- [ ] Toast notification system

## 🎯 Success Criteria

- [ ] Real-time notifications working across all user sessions
- [ ] Comprehensive activity logging for all major actions
- [ ] User preferences controlling notification delivery
- [ ] Performance optimized with proper indexing
- [ ] MSSQL integration for detailed internal logs

---

**Previous:** [001-initial-implementation.md](./001-initial-implementation.md)  
**Next:** 003-socket-io-implementation.md
