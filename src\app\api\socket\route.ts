import { NextRequest, NextResponse } from 'next/server'
import { Server as NetServer } from 'http'
import { Server as ServerIO } from 'socket.io'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { prisma } from '@/lib/prisma'

// This will be set when the Socket.io server is initialized
let io: ServerIO | null = null

export async function GET(request: NextRequest) {
  if (!io) {
    console.log('Initializing Socket.io server...')
    
    // In a production environment, you might want to use a separate server
    // For development, we'll create a simple HTTP server
    const httpServer = new NetServer()
    
    io = new ServerIO(httpServer, {
      cors: {
        origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
      },
    })

    // Authentication middleware
    io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token
        // In a real implementation, you'd verify the JWT token here
        // For now, we'll skip detailed auth verification
        next()
      } catch (error) {
        next(new Error('Authentication error'))
      }
    })

    io.on('connection', async (socket) => {
      console.log(`Socket connected: ${socket.id}`)

      // Handle user authentication after connection
      socket.on('authenticate', async (data: { userId: string }) => {
        try {
          // Store socket session
          await prisma.socketSession.create({
            data: {
              userId: data.userId,
              socketId: socket.id,
              isActive: true,
            },
          })

          // Join user-specific room
          socket.join(`user_${data.userId}`)
          socket.emit('authenticated', { success: true })
        } catch (error) {
          socket.emit('authentication_error', { error: 'Failed to authenticate' })
        }
      })

      socket.on('join_company', async (data: { companyId: string, userId: string }) => {
        try {
          // Verify access
          const access = await prisma.userCompanyAccess.findFirst({
            where: {
              userId: data.userId,
              companyId: data.companyId,
            },
          })

          if (access) {
            socket.join(`company_${data.companyId}`)
            socket.emit('company_joined', { companyId: data.companyId })
          } else {
            socket.emit('error', { message: 'Access denied' })
          }
        } catch (error) {
          socket.emit('error', { message: 'Failed to join company' })
        }
      })

      socket.on('disconnect', async () => {
        console.log(`Socket disconnected: ${socket.id}`)
        
        // Mark session as inactive
        await prisma.socketSession.updateMany({
          where: {
            socketId: socket.id,
            isActive: true,
          },
          data: {
            isActive: false,
          },
        })
      })
    })

    console.log('Socket.io server initialized')
  }

  return NextResponse.json({ message: 'Socket.io server running' })
}

// Helper function to get the Socket.io instance
export function getSocketIO(): ServerIO | null {
  return io
}

// Helper functions for emitting events
export async function emitToUser(userId: string, event: string, data: any) {
  if (io) {
    io.to(`user_${userId}`).emit(event, data)
  }
}

export async function emitToCompany(companyId: string, event: string, data: any) {
  if (io) {
    io.to(`company_${companyId}`).emit(event, data)
  }
}
