'use client'

import { useState } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { VersionBadge } from '@/components/ui/VersionBadge'

export default function SignInPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleEmailContinue = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setLoading(true)
    setError('')

    try {
      // Check if user exists (you can create an API endpoint for this)
      const response = await fetch('/api/auth/check-user', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })

      if (response.ok) {
        setShowPassword(true)
      } else {
        setError('User not found. Please check your email address.')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        setError('Invalid credentials')
      } else {
        const session = await getSession()
        if (session) {
          router.push('/select-country')
        }
      }
    } catch (error) {
      setError('An error occurred during sign in')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setShowPassword(false)
    setPassword('')
    setError('')
  }

  const handleGoogleSignIn = () => {
    signIn('google', { callbackUrl: '/select-country' })
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Panel - Login Form */}
      <div className="flex-1 flex items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {/* Version Badge */}
          <div className="flex justify-end mb-4">
            <VersionBadge showDetails={true} />
          </div>

          {/* Logo */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
              <span className="text-2xl font-bold text-white">P</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome!</h1>
            <p className="text-gray-600">Sign into your CORE account.</p>
          </div>

          {/* Form */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            {!showPassword ? (
              <form onSubmit={handleEmailContinue} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full"
                  />
                  <div className="mt-2 text-right">
                    <button type="button" className="text-sm text-blue-600 hover:text-blue-500">
                      Edit Email
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={loading || !email}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium"
                >
                  {loading ? 'Checking...' : 'CONTINUE'}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleSignIn} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="email-display" className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <div className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md border">
                    <span className="text-gray-900">{email}</span>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="text-sm text-blue-600 hover:text-blue-500"
                    >
                      Edit Email
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="w-full"
                  />
                  <div className="mt-2 text-right">
                    <button type="button" className="text-sm text-blue-600 hover:text-blue-500">
                      Reset Password
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={loading || !password}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium"
                >
                  {loading ? 'Signing in...' : 'Sign In'}
                </Button>
              </form>
            )}

            {/* Google Sign In - Only show on email step */}
            {!showPassword && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && (
              <>
                <div className="mt-6">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">Or continue with</span>
                    </div>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full mt-4"
                  onClick={handleGoogleSignIn}
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </Button>
              </>
            )}
          </div>

          {/* Footer Link */}
          <div className="text-center mt-6">
            <button className="text-blue-600 hover:text-blue-500 text-sm">
              Click here to see our latest features and updates.
            </button>
          </div>

          {/* Sign Up Link */}
          <div className="text-center mt-4">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link href="/signup" className="font-medium text-blue-600 hover:text-blue-500">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>

      {/* Right Panel - Marketing Content */}
      <div className="hidden lg:flex lg:flex-1 bg-gradient-to-br from-blue-900 to-blue-700 text-white p-8 items-center justify-center">
        <div className="max-w-md text-center">
          <div className="mb-6">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-500 rounded-full mb-4">
              <span className="text-xl font-bold">P</span>
            </div>
            <h2 className="text-sm font-medium text-blue-200 mb-2">PINNACLE CORE PRESENTS</h2>
            <h1 className="text-3xl font-bold mb-4">
              CORE Connect: Pinnacle<br />
              Q1 Innovations &<br />
              Improvements
            </h1>
            <p className="text-blue-200 mb-8">Now available on demand</p>
          </div>

          {/* Team Photos Placeholder */}
          <div className="flex justify-center space-x-4 mb-8">
            <div className="w-20 h-20 bg-blue-600 rounded-full border-4 border-orange-500"></div>
            <div className="w-20 h-20 bg-blue-600 rounded-full border-4 border-orange-500"></div>
            <div className="w-20 h-20 bg-blue-600 rounded-full border-4 border-orange-500"></div>
          </div>

          <Button className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-medium">
            Watch Now →
          </Button>
        </div>
      </div>
    </div>
  )
}
