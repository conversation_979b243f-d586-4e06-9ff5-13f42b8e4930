'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface Notification {
  id: string
  type: string
  title: string
  message: string
  isRead: boolean
  readAt: string | null
  link: string | null
  metadata: any
  priority: string
  createdAt: string
  updatedAt: string
  activityLog?: any
}

interface UseNotificationsReturn {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: string | null
  fetchNotifications: () => Promise<void>
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  refreshUnreadCount: () => Promise<void>
}

export function useNotifications(): UseNotificationsReturn {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchNotifications = useCallback(async () => {
    if (!session?.user) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/notifications?limit=50')
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      setNotifications(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [session?.user])

  const refreshUnreadCount = useCallback(async () => {
    if (!session?.user) return

    try {
      const response = await fetch('/api/notifications/unread-count')
      if (!response.ok) {
        throw new Error('Failed to fetch unread count')
      }

      const data = await response.json()
      setUnreadCount(data.count)
    } catch (err) {
      console.error('Failed to fetch unread count:', err)
    }
  }, [session?.user])

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_read' }),
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      )

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Failed to mark notification as read:', err)
    }
  }, [])

  const markAllAsRead = useCallback(async () => {
    const unreadNotifications = notifications.filter(n => !n.isRead)
    
    try {
      await Promise.all(
        unreadNotifications.map(notification => markAsRead(notification.id))
      )
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err)
    }
  }, [notifications, markAsRead])

  // Listen for real-time notification events
  useEffect(() => {
    const handleNewNotification = (event: CustomEvent) => {
      const newNotification = event.detail
      setNotifications(prev => [newNotification, ...prev])
      setUnreadCount(prev => prev + 1)
    }

    const handleNotificationRead = (event: CustomEvent) => {
      const { notificationId } = event.detail
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    }

    window.addEventListener('newNotification', handleNewNotification as EventListener)
    window.addEventListener('notificationRead', handleNotificationRead as EventListener)

    return () => {
      window.removeEventListener('newNotification', handleNewNotification as EventListener)
      window.removeEventListener('notificationRead', handleNotificationRead as EventListener)
    }
  }, [])

  // Initial data fetch
  useEffect(() => {
    if (session?.user) {
      fetchNotifications()
      refreshUnreadCount()
    }
  }, [session?.user, fetchNotifications, refreshUnreadCount])

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refreshUnreadCount,
  }
}
