'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface Country {
  id: string
  name: string
  code: string
}

export default function SelectCountryPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [countries, setCountries] = useState<Country[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCountry, setSelectedCountry] = useState<string>('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
      return
    }

    if (status === 'authenticated') {
      fetchCountries()
    }
  }, [status, router])

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/countries')
      if (response.ok) {
        const data = await response.json()
        setCountries(data)
      }
    } catch (error) {
      console.error('Error fetching countries:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCountrySelect = (countryId: string) => {
    setSelectedCountry(countryId)
  }

  const handleContinue = () => {
    if (selectedCountry) {
      router.push(`/select-company?country=${selectedCountry}`)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-extrabold text-gray-900">
            Welcome, {session?.user?.name}!
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Please select your country to continue
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Select Country</CardTitle>
            <CardDescription>
              Choose the country you want to operate in
            </CardDescription>
          </CardHeader>
          <CardContent>
            {countries.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No countries available</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {countries.map((country) => (
                  <div
                    key={country.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedCountry === country.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleCountrySelect(country.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">
                            {country.code}
                          </span>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">
                          {country.name}
                        </h3>
                        <p className="text-sm text-gray-500">{country.code}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedCountry && (
              <div className="mt-6 flex justify-end">
                <Button onClick={handleContinue}>
                  Continue
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
