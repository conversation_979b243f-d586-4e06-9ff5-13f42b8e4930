import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { prisma } from '@/lib/prisma'
import { EinvoiceService } from '@/services/EinvoiceService'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('company')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: companyId
        }
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this company' },
          { status: 403 }
        )
      }
    }

    // Get dashboard statistics using EinvoiceService
    const stats = await EinvoiceService.getInvoiceStats(companyId)

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
