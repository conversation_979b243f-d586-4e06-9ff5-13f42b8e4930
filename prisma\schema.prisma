// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  SUPER_ADMIN    // Platform-wide administration
  PORTAL_ADMIN   // Portal management and impersonation
  COMPANY_ADMIN  // Company-specific administration
  COMPANY_USER   // Regular company user
  USER           // Basic authenticated user
}

enum NotificationType {
  // System notifications
  SYSTEM_ANNOUNCEMENT
  SECURITY_ALERT
  SUBSCRIPTION_REMINDER

  // eInvoicing notifications
  EINVOICE_CREATED
  EINVOICE_STATUS_UPDATED
  EINVOICE_PAYMENT_RECEIVED
  EINVOICE_OVERDUE

  // User management
  USER_ADDED_TO_COMPANY
  USER_ROLE_CHANGED
  NEW_USER_REGISTERED

  // Transaction notifications
  TRANSACTION_COMPLETED
  TRANSACTION_FAILED
  PAYMENT_PROCESSED

  // Collaboration
  MENTIONED_IN_COMMENT
  TASK_ASSIGNED
  DOCUMENT_SHARED
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum ActorType {
  USER
  SYSTEM
  API
  WEBHOOK
  SCHEDULED_TASK
}

enum ActionType {
  // Authentication
  USER_LOGIN
  USER_LOGOUT
  USER_LOGIN_FAILED
  PASSWORD_CHANGED

  // User management
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  USER_ROLE_CHANGED
  USER_COMPANY_ACCESS_GRANTED
  USER_COMPANY_ACCESS_REVOKED

  // eInvoicing
  EINVOICE_CREATED
  EINVOICE_UPDATED
  EINVOICE_SENT
  EINVOICE_VIEWED
  EINVOICE_PAID
  EINVOICE_VOIDED

  // Company management
  COMPANY_CREATED
  COMPANY_UPDATED
  COMPANY_SETTINGS_CHANGED

  // System events
  SYSTEM_BACKUP_COMPLETED
  SYSTEM_MAINTENANCE_STARTED
  SYSTEM_ERROR_OCCURRED
}

enum ActionStatus {
  SUCCESS
  PENDING
  FAILED
  CANCELLED
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum EinvoiceStatus {
  DRAFT           // Being created/edited
  PENDING_REVIEW  // Awaiting approval
  APPROVED        // Approved for sending
  SENT            // Sent to recipient
  VIEWED          // Recipient has viewed
  PARTIALLY_PAID  // Partial payment received
  PAID            // Fully paid
  OVERDUE         // Past due date
  CANCELLED       // Cancelled before sending
  VOIDED          // Voided after sending
  DISPUTED        // Under dispute
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String?  // Nullable for OAuth users
  name      String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Enhanced relationships
  companyAccess     UserCompanyAccess[]
  notifications     Notification[]
  activityLogs      UserActivityLog[]
  preferences       UserPreference?
  dashboardConfigs  DashboardConfig[]
  socketSessions    SocketSession[]

  // eInvoicing relationships
  createdInvoices   Einvoice[]        @relation("InvoiceCreator")
  invoiceTemplates  EinvoiceTemplate[]
  invoiceHistory    EinvoiceHistory[]
  invoiceAttachments EinvoiceAttachment[]

  @@map("users")
}

model Country {
  id        String   @id @default(cuid())
  name      String   @unique
  code      String   @unique // ISO country code
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  companies Company[]

  @@map("countries")
}

model Company {
  id              String        @id @default(cuid())
  name            String
  status          CompanyStatus @default(ACTIVE)
  subscriptionEnd DateTime?
  environment     String        @default("production") // "production" or "sandbox"
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Foreign Keys
  countryId String
  country   Country @relation(fields: [countryId], references: [id])

  // Relations
  userAccess       UserCompanyAccess[]
  einvoices        Einvoice[]
  dashboardConfigs DashboardConfig[]
  activityLogs     UserActivityLog[]
  invoiceTemplates EinvoiceTemplate[]

  @@map("companies")
}

model UserCompanyAccess {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  userId    String
  companyId String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@unique([userId, companyId])
  @@map("user_company_access")
}

model Einvoice {
  id              String    @id @default(cuid())
  invoiceNumber   String    // Auto-generated or manual
  status          EinvoiceStatus @default(DRAFT)

  // Basic Information
  issueDate       DateTime
  dueDate         DateTime?
  currency        String    @default("USD")

  // Amounts
  subtotal        Decimal   @default(0)
  taxAmount       Decimal   @default(0)
  discountAmount  Decimal   @default(0)
  totalAmount     Decimal   @default(0)

  // Parties
  billFromName    String
  billFromAddress Json      // Structured address
  billFromEmail   String?
  billFromPhone   String?

  billToName      String
  billToAddress   Json      // Structured address
  billToEmail     String?
  billToPhone     String?

  // Additional Information
  description     String?
  notes           String?
  terms           String?

  // Metadata
  metadata        Json?     // Custom fields, integrations

  // Relationships
  companyId       String
  company         Company   @relation(fields: [companyId], references: [id])
  createdById     String?
  createdBy       User?     @relation("InvoiceCreator", fields: [createdById], references: [id])

  items           EinvoiceItem[]
  history         EinvoiceHistory[]
  attachments     EinvoiceAttachment[]

  // Timestamps
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  sentAt          DateTime?
  paidAt          DateTime?

  @@unique([companyId, invoiceNumber])
  @@index([companyId, status])
  @@index([createdAt])
  @@map("einvoices")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  readAt    DateTime?
  link      String?          // Deep link for navigation

  // Transaction logging integration
  metadata  Json?            // Rich transaction data
  priority  NotificationPriority @default(NORMAL)

  // Activity log relationship
  activityLogId String?            @unique
  activityLog   UserActivityLog?   @relation(fields: [activityLogId], references: [id], onDelete: SetNull)

  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([userId, isRead])
  @@index([createdAt])
  @@map("notifications")
}

model UserActivityLog {
  id          String    @id @default(cuid())

  // Actor information
  userId      String?   // User who performed the action
  user        User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  actorType   ActorType // Who/what initiated the action
  actorId     String?   // ID of the actor

  // Context information
  companyId   String?   // Company context
  company     Company?  @relation(fields: [companyId], references: [id], onDelete: SetNull)

  // Action details
  actionType  ActionType // Standardized action types
  targetType  String?    // Entity type affected
  targetId    String?    // ID of affected entity

  // Rich metadata and tracking
  details     Json?      // Specific action data
  metadata    Json?      // Additional context
  status      ActionStatus @default(SUCCESS)

  // Security and audit
  ipAddress   String?
  userAgent   String?
  sessionId   String?

  // Timestamps
  createdAt   DateTime  @default(now())

  // Notification relationship
  notifications Notification[]

  @@index([userId, createdAt])
  @@index([companyId, createdAt])
  @@index([actionType, createdAt])
  @@map("user_activity_logs")
}

model UserPreference {
  id              String   @id @default(cuid())
  userId          String   @unique
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Notification preferences
  receiveEmailNotifications Boolean @default(true)
  receiveInAppNotifications Boolean @default(true)
  receivePushNotifications  Boolean @default(false)

  // Granular notification settings
  notificationSettings Json? // Type-specific preferences

  // UI preferences
  theme           String @default("light")
  language        String @default("en")
  timezone        String @default("UTC")

  // Dashboard preferences
  defaultDashboard String?

  updatedAt       DateTime @updatedAt

  @@map("user_preferences")
}

model DashboardConfig {
  id       String @id @default(cuid())
  name     String
  layout   Json // Store dashboard layout configuration
  widgets  Json // Store widget configurations
  isActive Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign Keys
  userId    String
  companyId String?
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("dashboard_configs")
}

model SocketSession {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  socketId  String   @unique
  companyId String?  // Current company context
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId, isActive])
  @@map("socket_sessions")
}

model EinvoiceItem {
  id          String    @id @default(cuid())
  einvoiceId  String
  einvoice    Einvoice  @relation(fields: [einvoiceId], references: [id], onDelete: Cascade)

  // Item Details
  description String
  quantity    Decimal   @default(1)
  unitPrice   Decimal
  discount    Decimal   @default(0)
  taxRate     Decimal   @default(0)

  // Calculated Fields
  lineTotal   Decimal   // quantity * unitPrice - discount + tax

  // Additional Information
  productCode String?
  unit        String?   // e.g., "hours", "pieces", "kg"

  // Metadata
  metadata    Json?

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("einvoice_items")
}

model EinvoiceTemplate {
  id              String    @id @default(cuid())
  name            String
  description     String?

  // Template Data
  templateData    Json      // Stores default values for invoice creation

  // Settings
  isActive        Boolean   @default(true)
  isDefault       Boolean   @default(false)

  // Relationships
  companyId       String
  company         Company   @relation(fields: [companyId], references: [id])
  createdById     String
  createdBy       User      @relation(fields: [createdById], references: [id])

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@unique([companyId, name])
  @@map("einvoice_templates")
}

model EinvoiceHistory {
  id          String    @id @default(cuid())
  einvoiceId  String
  einvoice    Einvoice  @relation(fields: [einvoiceId], references: [id], onDelete: Cascade)

  // Status Change
  fromStatus  EinvoiceStatus?
  toStatus    EinvoiceStatus

  // Actor Information
  changedById String?
  changedBy   User?     @relation(fields: [changedById], references: [id])

  // Additional Information
  reason      String?
  notes       String?
  metadata    Json?

  createdAt   DateTime  @default(now())

  @@index([einvoiceId, createdAt])
  @@map("einvoice_history")
}

model EinvoiceAttachment {
  id          String    @id @default(cuid())
  einvoiceId  String
  einvoice    Einvoice  @relation(fields: [einvoiceId], references: [id], onDelete: Cascade)

  // File Information
  fileName    String
  fileSize    Int
  mimeType    String
  filePath    String    // Storage path or URL

  // Metadata
  description String?
  uploadedById String?
  uploadedBy  User?     @relation(fields: [uploadedById], references: [id])

  createdAt   DateTime  @default(now())

  @@map("einvoice_attachments")
}
