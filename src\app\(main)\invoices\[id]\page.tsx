'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { NotificationBell } from '@/components/ui/NotificationBell'
import { useSocket } from '@/hooks/useSocket'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  discount: number
  taxRate: number
  lineTotal: number
  productCode?: string
  unit?: string
}

interface InvoiceHistory {
  id: string
  fromStatus?: string
  toStatus: string
  reason?: string
  notes?: string
  createdAt: string
  changedBy?: {
    name: string
    email: string
  }
}

interface Invoice {
  id: string
  invoiceNumber: string
  status: string
  issueDate: string
  dueDate?: string
  currency: string
  subtotal: number
  taxAmount: number
  discountAmount: number
  totalAmount: number
  billFromName: string
  billFromAddress: any
  billFromEmail?: string
  billFromPhone?: string
  billToName: string
  billToAddress: any
  billToEmail?: string
  billToPhone?: string
  description?: string
  notes?: string
  terms?: string
  sentAt?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
  createdBy?: {
    name: string
    email: string
  }
  items: InvoiceItem[]
  history: InvoiceHistory[]
}

export default function InvoiceDetailPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const { isConnected } = useSocket()

  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<string>('')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
      return
    }

    const companyId = sessionStorage.getItem('selectedCompany')
    if (!companyId) {
      router.push('/select-country')
      return
    }

    setSelectedCompany(companyId)

    if (status === 'authenticated' && params.id) {
      fetchInvoice(params.id as string)
    }
  }, [status, router, params.id])

  const fetchInvoice = async (invoiceId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/invoices/${invoiceId}`)
      if (response.ok) {
        const data = await response.json()
        setInvoice(data)
      } else {
        console.error('Failed to fetch invoice')
        router.push('/invoices')
      }
    } catch (error) {
      console.error('Error fetching invoice:', error)
      router.push('/invoices')
    } finally {
      setLoading(false)
    }
  }

  const updateInvoiceStatus = async (newStatus: string, reason?: string) => {
    if (!invoice) return

    setUpdating(true)
    try {
      const response = await fetch(`/api/invoices/${invoice.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          reason,
        }),
      })

      if (response.ok) {
        const updatedInvoice = await response.json()
        setInvoice(updatedInvoice)
      } else {
        const error = await response.json()
        alert(`Error updating status: ${error.message}`)
      }
    } catch (error) {
      console.error('Error updating invoice status:', error)
      alert('Error updating invoice status')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800'
      case 'PENDING_REVIEW':
        return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED':
        return 'bg-blue-100 text-blue-800'
      case 'SENT':
        return 'bg-purple-100 text-purple-800'
      case 'PAID':
        return 'bg-green-100 text-green-800'
      case 'OVERDUE':
        return 'bg-red-100 text-red-800'
      case 'CANCELLED':
      case 'VOIDED':
        return 'bg-gray-100 text-gray-600'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatAddress = (address: any) => {
    if (!address) return ''
    return `${address.street || ''}, ${address.city || ''}, ${address.state || ''} ${address.zipCode || ''}, ${address.country || ''}`.replace(/^,\s*|,\s*$/g, '')
  }

  const getAvailableActions = () => {
    if (!invoice) return []

    const actions = []

    switch (invoice.status) {
      case 'DRAFT':
        actions.push(
          { label: 'Submit for Review', status: 'PENDING_REVIEW', variant: 'default' },
          { label: 'Cancel', status: 'CANCELLED', variant: 'ghost' }
        )
        break
      case 'PENDING_REVIEW':
        actions.push(
          { label: 'Approve', status: 'APPROVED', variant: 'default' },
          { label: 'Back to Draft', status: 'DRAFT', variant: 'ghost' }
        )
        break
      case 'APPROVED':
        actions.push(
          { label: 'Send Invoice', status: 'SENT', variant: 'default' },
          { label: 'Cancel', status: 'CANCELLED', variant: 'ghost' }
        )
        break
      case 'SENT':
        actions.push(
          { label: 'Mark as Paid', status: 'PAID', variant: 'default' },
          { label: 'Void Invoice', status: 'VOIDED', variant: 'ghost' }
        )
        break
    }

    return actions
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Invoice Not Found</h2>
          <Button onClick={() => router.push('/invoices')}>
            Back to Invoices
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Invoice {invoice.invoiceNumber}
              </h1>
              <p className="text-sm text-gray-600">
                Created on {formatDate(invoice.createdAt)}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <NotificationBell />
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="text-sm text-gray-600">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <Button
                variant="ghost"
                onClick={() => router.push('/invoices')}
              >
                Back to Invoices
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Status and Actions */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Invoice Status</CardTitle>
                  <CardDescription>
                    Current status and available actions
                  </CardDescription>
                </div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(invoice.status)}`}>
                  {invoice.status.replace('_', ' ')}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {getAvailableActions().map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant as any}
                    onClick={() => updateInvoiceStatus(action.status)}
                    disabled={updating}
                    className={action.variant === 'default' ? 'bg-blue-600 hover:bg-blue-700' : ''}
                  >
                    {updating ? 'Updating...' : action.label}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Invoice Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Bill From */}
            <Card>
              <CardHeader>
                <CardTitle>Bill From</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-medium">{invoice.billFromName}</div>
                  {invoice.billFromEmail && (
                    <div className="text-sm text-gray-600">{invoice.billFromEmail}</div>
                  )}
                  {invoice.billFromPhone && (
                    <div className="text-sm text-gray-600">{invoice.billFromPhone}</div>
                  )}
                  {invoice.billFromAddress && (
                    <div className="text-sm text-gray-600">
                      {formatAddress(invoice.billFromAddress)}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Bill To */}
            <Card>
              <CardHeader>
                <CardTitle>Bill To</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-medium">{invoice.billToName}</div>
                  {invoice.billToEmail && (
                    <div className="text-sm text-gray-600">{invoice.billToEmail}</div>
                  )}
                  {invoice.billToPhone && (
                    <div className="text-sm text-gray-600">{invoice.billToPhone}</div>
                  )}
                  {invoice.billToAddress && (
                    <div className="text-sm text-gray-600">
                      {formatAddress(invoice.billToAddress)}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Invoice Information */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                  <div className="text-sm text-gray-900">{formatDate(invoice.issueDate)}</div>
                </div>
                {invoice.dueDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Due Date</label>
                    <div className="text-sm text-gray-900">{formatDate(invoice.dueDate)}</div>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700">Currency</label>
                  <div className="text-sm text-gray-900">{invoice.currency}</div>
                </div>
                {invoice.sentAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Sent Date</label>
                    <div className="text-sm text-gray-900">{formatDate(invoice.sentAt)}</div>
                  </div>
                )}
                {invoice.paidAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Paid Date</label>
                    <div className="text-sm text-gray-900">{formatDate(invoice.paidAt)}</div>
                  </div>
                )}
              </div>

              {invoice.description && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <div className="text-sm text-gray-900">{invoice.description}</div>
                </div>
              )}

              {invoice.terms && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700">Terms & Conditions</label>
                  <div className="text-sm text-gray-900">{invoice.terms}</div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Invoice Items */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Qty
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Discount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tax
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {invoice.items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{item.description}</div>
                          {item.productCode && (
                            <div className="text-sm text-gray-500">Code: {item.productCode}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.quantity} {item.unit && `${item.unit}`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.unitPrice, invoice.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.discount}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.taxRate}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatCurrency(item.lineTotal, invoice.currency)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Invoice Totals */}
              <div className="mt-6 flex justify-end">
                <div className="w-64 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal:</span>
                    <span className="text-sm text-gray-900">{formatCurrency(invoice.subtotal, invoice.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Discount:</span>
                    <span className="text-sm text-gray-900">-{formatCurrency(invoice.discountAmount, invoice.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tax:</span>
                    <span className="text-sm text-gray-900">{formatCurrency(invoice.taxAmount, invoice.currency)}</span>
                  </div>
                  <hr />
                  <div className="flex justify-between">
                    <span className="text-base font-medium text-gray-900">Total:</span>
                    <span className="text-base font-bold text-gray-900">{formatCurrency(invoice.totalAmount, invoice.currency)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Invoice History */}
          {invoice.history.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Status History</CardTitle>
                <CardDescription>
                  Track of all status changes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoice.history.map((entry) => (
                    <div key={entry.id} className="border-l-4 border-blue-200 pl-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {entry.fromStatus && `${entry.fromStatus} → `}{entry.toStatus}
                          </div>
                          {entry.reason && (
                            <div className="text-sm text-gray-600">{entry.reason}</div>
                          )}
                          {entry.notes && (
                            <div className="text-sm text-gray-500 mt-1">{entry.notes}</div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">{formatDate(entry.createdAt)}</div>
                          {entry.changedBy && (
                            <div className="text-sm text-gray-400">{entry.changedBy.name}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  )
}
