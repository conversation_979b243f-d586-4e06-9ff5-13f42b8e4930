import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const countryId = searchParams.get('country')

    let whereClause: any = {}

    if (countryId) {
      whereClause.countryId = countryId
    }

    // If user is not a portal admin, only show companies they have access to
    if (session.user.role !== 'PORTAL_ADMIN') {
      whereClause.userAccess = {
        some: {
          userId: session.user.id
        }
      }
    }

    const companies = await prisma.company.findMany({
      where: whereClause,
      include: {
        country: {
          select: {
            name: true,
            code: true
          }
        },
        userAccess: {
          where: {
            userId: session.user.id
          },
          select: {
            id: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    return NextResponse.json(companies)
  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'PORTAL_ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { name, countryId, environment = 'production', subscriptionEnd } = await request.json()

    if (!name || !countryId) {
      return NextResponse.json(
        { error: 'Name and country are required' },
        { status: 400 }
      )
    }

    const company = await prisma.company.create({
      data: {
        name,
        countryId,
        environment,
        subscriptionEnd: subscriptionEnd ? new Date(subscriptionEnd) : null,
      },
      include: {
        country: {
          select: {
            name: true,
            code: true
          }
        }
      }
    })

    return NextResponse.json(company, { status: 201 })
  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
