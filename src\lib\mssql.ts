import sql from 'mssql'

const config: sql.config = {
  connectionString: process.env.MSSQL_LOG_DATABASE_URL,
  options: {
    encrypt: true,
    trustServerCertificate: true,
  },
}

let pool: sql.ConnectionPool | null = null

export async function getMSSQLConnection() {
  if (!pool) {
    pool = new sql.ConnectionPool(config)
    await pool.connect()
  }
  return pool
}

export async function logToMSSQL(
  level: 'info' | 'warn' | 'error',
  message: string,
  metadata?: any
) {
  try {
    if (!process.env.MSSQL_LOG_DATABASE_URL) {
      console.log(`[${level.toUpperCase()}] ${message}`, metadata)
      return
    }

    const connection = await getMSSQLConnection()
    const request = connection.request()
    
    await request
      .input('level', sql.VarChar, level)
      .input('message', sql.Text, message)
      .input('metadata', sql.Text, metadata ? JSON.stringify(metadata) : null)
      .input('timestamp', sql.DateTime, new Date())
      .query(`
        INSERT INTO system_logs (level, message, metadata, timestamp)
        VALUES (@level, @message, @metadata, @timestamp)
      `)
  } catch (error) {
    console.error('Failed to log to MSSQL:', error)
    console.log(`[${level.toUpperCase()}] ${message}`, metadata)
  }
}

export async function closeMSSQLConnection() {
  if (pool) {
    await pool.close()
    pool = null
  }
}
