'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { NotificationBell } from '@/components/ui/NotificationBell'
import { useSocket } from '@/hooks/useSocket'

interface User {
  id: string
  email: string
  name?: string
  role: string
  createdAt: string
  updatedAt: string
  companyAccess: {
    id: string
    company: {
      id: string
      name: string
      status: string
    }
  }[]
}

interface UserListResponse {
  users: User[]
  total: number
  hasMore: boolean
}

export default function UsersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { isConnected } = useSocket()
  
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('')
  const [selectedCompany, setSelectedCompany] = useState<string>('')
  const [total, setTotal] = useState(0)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
      return
    }

    // Check if user has admin privileges
    if (session?.user?.role !== 'PORTAL_ADMIN' && session?.user?.role !== 'COMPANY_ADMIN') {
      router.push('/dashboard')
      return
    }

    // Get selected company from session storage
    const companyId = sessionStorage.getItem('selectedCompany')
    if (!companyId) {
      router.push('/select-country')
      return
    }

    setSelectedCompany(companyId)
    
    if (status === 'authenticated') {
      fetchUsers(companyId)
    }
  }, [status, router, session])

  const fetchUsers = async (companyId: string, search?: string, role?: string) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        company: companyId,
        limit: '20',
        offset: '0',
      })

      if (search) params.append('search', search)
      if (role) params.append('role', role)

      const response = await fetch(`/api/users?${params}`)
      if (response.ok) {
        const data: UserListResponse = await response.json()
        setUsers(data.users)
        setTotal(data.total)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    if (selectedCompany) {
      fetchUsers(selectedCompany, searchTerm, roleFilter)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'bg-red-100 text-red-800'
      case 'PORTAL_ADMIN':
        return 'bg-purple-100 text-purple-800'
      case 'COMPANY_ADMIN':
        return 'bg-blue-100 text-blue-800'
      case 'COMPANY_USER':
        return 'bg-green-100 text-green-800'
      case 'USER':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // Refresh the user list
        fetchUsers(selectedCompany, searchTerm, roleFilter)
      } else {
        const error = await response.json()
        alert(`Error deleting user: ${error.message}`)
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      alert('Error deleting user')
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                User Management
              </h1>
              <p className="text-sm text-gray-600">
                Manage users and their access permissions
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <NotificationBell />
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="text-sm text-gray-600">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <Button onClick={() => router.push('/dashboard')}>
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Filters and Search */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Filter Users</CardTitle>
              <CardDescription>
                Search and filter users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <div className="w-full md:w-48">
                  <select
                    className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                  >
                    <option value="">All Roles</option>
                    <option value="SUPER_ADMIN">Super Admin</option>
                    <option value="PORTAL_ADMIN">Portal Admin</option>
                    <option value="COMPANY_ADMIN">Company Admin</option>
                    <option value="COMPANY_USER">Company User</option>
                    <option value="USER">User</option>
                  </select>
                </div>
                <Button onClick={handleSearch}>
                  Search
                </Button>
                <Button 
                  onClick={() => router.push('/users/create')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Add User
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* User List */}
          <Card>
            <CardHeader>
              <CardTitle>Users ({total})</CardTitle>
              <CardDescription>
                Manage user accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {users.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No users found</p>
                  <Button 
                    className="mt-4"
                    onClick={() => router.push('/users/create')}
                  >
                    Add Your First User
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Role
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Companies
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {user.name || 'No name'}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                              {user.role.replace('_', ' ')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {user.companyAccess.length} companies
                            </div>
                            <div className="text-sm text-gray-500">
                              {user.companyAccess.slice(0, 2).map(access => access.company.name).join(', ')}
                              {user.companyAccess.length > 2 && '...'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(user.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/users/${user.id}`)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              Delete
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
