import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges or is accessing their own data
    if (session.user.role !== 'PORTAL_ADMIN' && 
        session.user.role !== 'COMPANY_ADMIN' && 
        session.user.id !== params.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        companyAccess: {
          include: {
            company: {
              select: {
                id: true,
                name: true,
                status: true,
                environment: true
              }
            }
          }
        },
        preferences: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user

    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges or is updating their own data
    if (session.user.role !== 'PORTAL_ADMIN' && 
        session.user.role !== 'COMPANY_ADMIN' && 
        session.user.id !== params.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { email, password, name, role, companyIds } = body

    // Get current user
    const currentUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Validate role permissions
    if (role && session.user.role === 'COMPANY_ADMIN' && 
        (role === 'SUPER_ADMIN' || role === 'PORTAL_ADMIN')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign this role' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateData: any = {}
    if (email) updateData.email = email
    if (name !== undefined) updateData.name = name
    if (role) updateData.role = role as UserRole

    // Hash password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 12)
    }

    // Update user and company access
    const updatedUser = await prisma.$transaction(async (tx) => {
      const user = await tx.user.update({
        where: { id: params.id },
        data: updateData
      })

      // Update company access if provided and user has permission
      if (companyIds && (session.user.role === 'PORTAL_ADMIN' || session.user.role === 'COMPANY_ADMIN')) {
        // Remove existing company access
        await tx.userCompanyAccess.deleteMany({
          where: { userId: params.id }
        })

        // Add new company access
        if (companyIds.length > 0) {
          await tx.userCompanyAccess.createMany({
            data: companyIds.map((companyId: string) => ({
              userId: params.id,
              companyId
            }))
          })
        }
      }

      return user
    })

    // Return user without password
    const { password: _, ...userWithoutPassword } = updatedUser

    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin privileges
    if (session.user.role !== 'PORTAL_ADMIN' && session.user.role !== 'COMPANY_ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Prevent self-deletion
    if (session.user.id === params.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    // Get user to check permissions
    const userToDelete = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!userToDelete) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Company admins cannot delete portal admins or super admins
    if (session.user.role === 'COMPANY_ADMIN' && 
        (userToDelete.role === 'SUPER_ADMIN' || userToDelete.role === 'PORTAL_ADMIN')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete this user' },
        { status: 403 }
      )
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
