'use client'

import { useState, useEffect } from 'react'
import { Button } from './Button'

interface VersionInfo {
  current: string
  latest?: string
  releaseDate: string
  changelog: string[]
  hasUpdate?: boolean
}

interface VersionBadgeProps {
  showDetails?: boolean
  className?: string
  onClick?: () => void
}

export function VersionBadge({ showDetails = false, className = '', onClick }: VersionBadgeProps) {
  const [versionInfo, setVersionInfo] = useState<VersionInfo>({
    current: '2025.05.3',
    releaseDate: '2025-05-28',
    changelog: [
      'Enhanced login page with progressive authentication',
      'Added version control and developer settings',
      'Improved user management interface',
      'Fixed authentication middleware compatibility'
    ]
  })
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    // Get version from package.json or environment
    const version = process.env.NEXT_PUBLIC_APP_VERSION || '2025.05.3'
    setVersionInfo(prev => ({ ...prev, current: version }))
  }, [])

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else if (showDetails) {
      setShowModal(true)
    }
  }

  return (
    <>
      <span 
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 cursor-pointer hover:bg-gray-200 transition-colors ${className}`}
        onClick={handleClick}
      >
        Version: {versionInfo.current}
        {showDetails && (
          <button className="ml-1 text-gray-400 hover:text-gray-600">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </button>
        )}
        {versionInfo.hasUpdate && (
          <span className="ml-1 w-2 h-2 bg-orange-500 rounded-full"></span>
        )}
      </span>

      {/* Version Details Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Version Information</h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="font-medium">Current Version:</span>
                <span className="text-gray-600">{versionInfo.current}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Release Date:</span>
                <span className="text-gray-600">{versionInfo.releaseDate}</span>
              </div>

              {versionInfo.latest && versionInfo.latest !== versionInfo.current && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <p className="text-sm text-blue-800 font-medium">Update Available!</p>
                  <p className="text-xs text-blue-600">Version {versionInfo.latest} is now available.</p>
                </div>
              )}

              <div>
                <h4 className="font-medium mb-2">Recent Changes:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {versionInfo.changelog.map((change, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {change}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowModal(false)}
                  className="flex-1"
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setShowModal(false)
                    window.open('/settings', '_blank')
                  }}
                  className="flex-1"
                >
                  View Settings
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Hook for accessing version info in other components
export function useVersionInfo() {
  const [versionInfo, setVersionInfo] = useState<VersionInfo>({
    current: '2025.05.3',
    releaseDate: '2025-05-28',
    changelog: []
  })

  useEffect(() => {
    const version = process.env.NEXT_PUBLIC_APP_VERSION || '2025.05.3'
    setVersionInfo(prev => ({ ...prev, current: version }))
  }, [])

  const checkForUpdates = async () => {
    // Simulate API call to check for updates
    return new Promise<VersionInfo>((resolve) => {
      setTimeout(() => {
        resolve({
          ...versionInfo,
          latest: '2025.05.4',
          hasUpdate: true,
          changelog: [
            'New dashboard widgets',
            'Enhanced notification system',
            'Performance improvements',
            'Bug fixes and security updates'
          ]
        })
      }, 1000)
    })
  }

  return {
    versionInfo,
    checkForUpdates,
    setVersionInfo
  }
}
