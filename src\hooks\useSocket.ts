'use client'

import { useEffect, useRef, useState } from 'react'
import { useSession } from 'next-auth/react'
import { io, Socket } from 'socket.io-client'

interface UseSocketReturn {
  socket: Socket | null
  isConnected: boolean
  joinCompany: (companyId: string) => void
  leaveCompany: (companyId: string) => void
  markNotificationRead: (notificationId: string) => void
}

export function useSocket(): UseSocketReturn {
  const { data: session } = useSession()
  const [isConnected, setIsConnected] = useState(false)
  const socketRef = useRef<Socket | null>(null)

  useEffect(() => {
    if (!session?.user) {
      return
    }

    // Initialize socket connection
    const socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin, {
      path: '/api/socket',
      auth: {
        token: 'user-token', // In production, use actual JWT token
      },
    })

    socketRef.current = socket

    socket.on('connect', () => {
      console.log('Socket connected')
      setIsConnected(true)
      
      // Authenticate with user ID
      socket.emit('authenticate', { userId: session.user.id })
    })

    socket.on('disconnect', () => {
      console.log('Socket disconnected')
      setIsConnected(false)
    })

    socket.on('authenticated', (data) => {
      console.log('Socket authenticated:', data)
    })

    socket.on('authentication_error', (error) => {
      console.error('Socket authentication error:', error)
    })

    socket.on('new_notification', (notification) => {
      console.log('New notification received:', notification)
      
      // Dispatch custom event for notification components to listen to
      window.dispatchEvent(
        new CustomEvent('newNotification', { detail: notification })
      )
      
      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
        })
      }
    })

    socket.on('company_notification', (notification) => {
      console.log('Company notification received:', notification)
      
      window.dispatchEvent(
        new CustomEvent('companyNotification', { detail: notification })
      )
    })

    socket.on('activity_update', (activityLog) => {
      console.log('Activity update received:', activityLog)
      
      window.dispatchEvent(
        new CustomEvent('activityUpdate', { detail: activityLog })
      )
    })

    socket.on('company_joined', (data) => {
      console.log('Joined company:', data.companyId)
    })

    socket.on('company_left', (data) => {
      console.log('Left company:', data.companyId)
    })

    socket.on('notification_marked_read', (data) => {
      console.log('Notification marked as read:', data.notificationId)
      
      window.dispatchEvent(
        new CustomEvent('notificationRead', { detail: data })
      )
    })

    socket.on('error', (error) => {
      console.error('Socket error:', error)
    })

    // Cleanup on unmount
    return () => {
      socket.disconnect()
      socketRef.current = null
      setIsConnected(false)
    }
  }, [session?.user])

  const joinCompany = (companyId: string) => {
    if (socketRef.current && session?.user) {
      socketRef.current.emit('join_company', {
        companyId,
        userId: session.user.id,
      })
    }
  }

  const leaveCompany = (companyId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('leave_company', { companyId })
    }
  }

  const markNotificationRead = (notificationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('mark_notification_read', notificationId)
    }
  }

  return {
    socket: socketRef.current,
    isConnected,
    joinCompany,
    leaveCompany,
    markNotificationRead,
  }
}
