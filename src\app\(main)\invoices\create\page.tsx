'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { NotificationBell } from '@/components/ui/NotificationBell'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  discount: number
  taxRate: number
  lineTotal: number
}

interface CreateInvoiceData {
  issueDate: string
  dueDate: string
  currency: string
  billFromName: string
  billFromAddress: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  billFromEmail: string
  billFromPhone: string
  billToName: string
  billToAddress: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  billToEmail: string
  billToPhone: string
  description: string
  notes: string
  terms: string
  items: Omit<InvoiceItem, 'id' | 'lineTotal'>[]
}

export default function CreateInvoicePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [loading, setLoading] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<string>('')
  const [formData, setFormData] = useState<CreateInvoiceData>({
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    currency: 'USD',
    billFromName: '',
    billFromAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: ''
    },
    billFromEmail: '',
    billFromPhone: '',
    billToName: '',
    billToAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: ''
    },
    billToEmail: '',
    billToPhone: '',
    description: '',
    notes: '',
    terms: '',
    items: [{
      description: '',
      quantity: 1,
      unitPrice: 0,
      discount: 0,
      taxRate: 0
    }]
  })

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signin')
      return
    }

    const companyId = sessionStorage.getItem('selectedCompany')
    if (!companyId) {
      router.push('/select-country')
      return
    }

    setSelectedCompany(companyId)
  }, [status, router])

  const calculateLineTotal = (item: Omit<InvoiceItem, 'id' | 'lineTotal'>) => {
    const subtotal = item.quantity * item.unitPrice
    const discountAmount = subtotal * (item.discount / 100)
    const afterDiscount = subtotal - discountAmount
    const taxAmount = afterDiscount * (item.taxRate / 100)
    return afterDiscount + taxAmount
  }

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    const discountAmount = formData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice * item.discount / 100), 0)
    const taxAmount = formData.items.reduce((sum, item) => {
      const afterDiscount = (item.quantity * item.unitPrice) - (item.quantity * item.unitPrice * item.discount / 100)
      return sum + (afterDiscount * item.taxRate / 100)
    }, 0)
    const totalAmount = subtotal - discountAmount + taxAmount

    return { subtotal, discountAmount, taxAmount, totalAmount }
  }

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        description: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 0
      }]
    }))
  }

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index)
      }))
    }
  }

  const updateItem = (index: number, field: keyof Omit<InvoiceItem, 'id' | 'lineTotal'>, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedCompany) return

    setLoading(true)
    try {
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          companyId: selectedCompany,
          items: formData.items.map(item => ({
            ...item,
            quantity: Number(item.quantity),
            unitPrice: Number(item.unitPrice),
            discount: Number(item.discount),
            taxRate: Number(item.taxRate)
          }))
        }),
      })

      if (response.ok) {
        const invoice = await response.json()
        router.push(`/invoices/${invoice.id}`)
      } else {
        const error = await response.json()
        alert(`Error creating invoice: ${error.message}`)
      }
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert('Error creating invoice')
    } finally {
      setLoading(false)
    }
  }

  const totals = calculateTotals()

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Create Invoice
              </h1>
              <p className="text-sm text-gray-600">
                Create a new electronic invoice
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <NotificationBell />
              <Button
                variant="ghost"
                onClick={() => router.push('/invoices')}
              >
                Back to Invoices
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Invoice dates and currency settings
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Issue Date *
                </label>
                <Input
                  type="date"
                  value={formData.issueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, issueDate: e.target.value }))}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date
                </label>
                <Input
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency *
                </label>
                <select
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
                  value={formData.currency}
                  onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                  required
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="CAD">CAD - Canadian Dollar</option>
                  <option value="AUD">AUD - Australian Dollar</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Bill From Section */}
          <Card>
            <CardHeader>
              <CardTitle>Bill From</CardTitle>
              <CardDescription>
                Your company information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name *
                  </label>
                  <Input
                    value={formData.billFromName}
                    onChange={(e) => setFormData(prev => ({ ...prev, billFromName: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <Input
                    type="email"
                    value={formData.billFromEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, billFromEmail: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <Input
                    value={formData.billFromPhone}
                    onChange={(e) => setFormData(prev => ({ ...prev, billFromPhone: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address
                  </label>
                  <Input
                    value={formData.billFromAddress.street}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billFromAddress: { ...prev.billFromAddress, street: e.target.value }
                    }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <Input
                    value={formData.billFromAddress.city}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billFromAddress: { ...prev.billFromAddress, city: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    State
                  </label>
                  <Input
                    value={formData.billFromAddress.state}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billFromAddress: { ...prev.billFromAddress, state: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ZIP Code
                  </label>
                  <Input
                    value={formData.billFromAddress.zipCode}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billFromAddress: { ...prev.billFromAddress, zipCode: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <Input
                    value={formData.billFromAddress.country}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billFromAddress: { ...prev.billFromAddress, country: e.target.value }
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bill To Section */}
          <Card>
            <CardHeader>
              <CardTitle>Bill To</CardTitle>
              <CardDescription>
                Client information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client Name *
                  </label>
                  <Input
                    value={formData.billToName}
                    onChange={(e) => setFormData(prev => ({ ...prev, billToName: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <Input
                    type="email"
                    value={formData.billToEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, billToEmail: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <Input
                    value={formData.billToPhone}
                    onChange={(e) => setFormData(prev => ({ ...prev, billToPhone: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address
                  </label>
                  <Input
                    value={formData.billToAddress.street}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billToAddress: { ...prev.billToAddress, street: e.target.value }
                    }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <Input
                    value={formData.billToAddress.city}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billToAddress: { ...prev.billToAddress, city: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    State
                  </label>
                  <Input
                    value={formData.billToAddress.state}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billToAddress: { ...prev.billToAddress, state: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ZIP Code
                  </label>
                  <Input
                    value={formData.billToAddress.zipCode}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billToAddress: { ...prev.billToAddress, zipCode: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <Input
                    value={formData.billToAddress.country}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      billToAddress: { ...prev.billToAddress, country: e.target.value }
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Invoice Items */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Items</CardTitle>
              <CardDescription>
                Add items to your invoice
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {formData.items.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Remove
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description *
                        </label>
                        <Input
                          value={item.description}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          placeholder="Item description"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Quantity *
                        </label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Unit Price *
                        </label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unitPrice}
                          onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Discount (%)
                        </label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          value={item.discount}
                          onChange={(e) => updateItem(index, 'discount', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tax Rate (%)
                        </label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.taxRate}
                          onChange={(e) => updateItem(index, 'taxRate', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>

                    <div className="text-right">
                      <span className="text-sm font-medium text-gray-700">
                        Line Total: {new Intl.NumberFormat('en-US', {
                          style: 'currency',
                          currency: formData.currency,
                        }).format(calculateLineTotal(item))}
                      </span>
                    </div>
                  </div>
                ))}

                <Button
                  type="button"
                  variant="ghost"
                  onClick={addItem}
                  className="w-full border-2 border-dashed border-gray-300 hover:border-gray-400"
                >
                  + Add Item
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Information</CardTitle>
              <CardDescription>
                Optional details and terms
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  className="w-full h-20 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of the invoice"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  className="w-full h-20 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Internal notes (not visible to client)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Terms & Conditions
                </label>
                <textarea
                  className="w-full h-20 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={formData.terms}
                  onChange={(e) => setFormData(prev => ({ ...prev, terms: e.target.value }))}
                  placeholder="Payment terms and conditions"
                />
              </div>
            </CardContent>
          </Card>

          {/* Invoice Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: formData.currency,
                  }).format(totals.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Discount:</span>
                  <span>-{new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: formData.currency,
                  }).format(totals.discountAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>{new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: formData.currency,
                  }).format(totals.taxAmount)}</span>
                </div>
                <hr />
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>{new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: formData.currency,
                  }).format(totals.totalAmount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => router.push('/invoices')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Creating...' : 'Create Invoice'}
            </Button>
          </div>
        </form>
      </main>
    </div>
  )
}
