import { prisma } from '@/lib/prisma'
import { emitToUser, emitToCompany } from '@/app/api/socket/route'
import { 
  NotificationType, 
  NotificationPriority, 
  ActionType, 
  ActorType,
  ActionStatus 
} from '@prisma/client'

export interface CreateNotificationData {
  userId: string
  type: NotificationType
  title: string
  message: string
  link?: string
  metadata?: any
  priority?: NotificationPriority
  activityLogId?: string
}

export interface CreateActivityLogData {
  userId?: string
  actorType: ActorType
  actorId?: string
  companyId?: string
  actionType: ActionType
  targetType?: string
  targetId?: string
  details?: any
  metadata?: any
  status?: ActionStatus
  ipAddress?: string
  userAgent?: string
  sessionId?: string
}

export class NotificationService {
  /**
   * Create a new notification and emit it in real-time
   */
  static async createNotification(data: CreateNotificationData) {
    try {
      const notification = await prisma.notification.create({
        data: {
          userId: data.userId,
          type: data.type,
          title: data.title,
          message: data.message,
          link: data.link,
          metadata: data.metadata,
          priority: data.priority || NotificationPriority.NORMAL,
          activityLogId: data.activityLogId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          activityLog: true,
        },
      })

      // Check user preferences before emitting
      const userPrefs = await prisma.userPreference.findUnique({
        where: { userId: data.userId },
      })

      if (userPrefs?.receiveInAppNotifications !== false) {
        // Emit real-time notification
        await emitToUser(data.userId, 'new_notification', notification)
      }

      return notification
    } catch (error) {
      console.error('Failed to create notification:', error)
      throw error
    }
  }

  /**
   * Create multiple notifications for different users
   */
  static async createBulkNotifications(notifications: CreateNotificationData[]) {
    try {
      const createdNotifications = await Promise.all(
        notifications.map(notification => this.createNotification(notification))
      )
      return createdNotifications
    } catch (error) {
      console.error('Failed to create bulk notifications:', error)
      throw error
    }
  }

  /**
   * Create an activity log entry
   */
  static async createActivityLog(data: CreateActivityLogData) {
    try {
      const activityLog = await prisma.userActivityLog.create({
        data: {
          userId: data.userId,
          actorType: data.actorType,
          actorId: data.actorId,
          companyId: data.companyId,
          actionType: data.actionType,
          targetType: data.targetType,
          targetId: data.targetId,
          details: data.details,
          metadata: data.metadata,
          status: data.status || ActionStatus.SUCCESS,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          sessionId: data.sessionId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      })

      // Emit activity update to company if applicable
      if (data.companyId) {
        await emitToCompany(data.companyId, 'activity_update', activityLog)
      }

      return activityLog
    } catch (error) {
      console.error('Failed to create activity log:', error)
      throw error
    }
  }

  /**
   * Create activity log and generate notifications based on the action
   */
  static async logActivityAndNotify(
    activityData: CreateActivityLogData,
    notificationTargets?: string[]
  ) {
    try {
      // Create the activity log
      const activityLog = await this.createActivityLog(activityData)

      // Generate notifications based on action type
      if (notificationTargets && notificationTargets.length > 0) {
        const notificationData = this.generateNotificationFromActivity(
          activityLog,
          activityData.actionType
        )

        if (notificationData) {
          const notifications = notificationTargets.map(userId => ({
            ...notificationData,
            userId,
            activityLogId: activityLog.id,
          }))

          await this.createBulkNotifications(notifications)
        }
      }

      return activityLog
    } catch (error) {
      console.error('Failed to log activity and notify:', error)
      throw error
    }
  }

  /**
   * Generate notification data based on activity type
   */
  private static generateNotificationFromActivity(
    activityLog: any,
    actionType: ActionType
  ): Omit<CreateNotificationData, 'userId' | 'activityLogId'> | null {
    const actorName = activityLog.user?.name || 'System'
    const companyName = activityLog.company?.name || 'Unknown Company'

    switch (actionType) {
      case ActionType.EINVOICE_CREATED:
        return {
          type: NotificationType.EINVOICE_CREATED,
          title: 'New Invoice Created',
          message: `${actorName} created a new invoice`,
          priority: NotificationPriority.NORMAL,
          metadata: activityLog.details,
        }

      case ActionType.EINVOICE_PAID:
        return {
          type: NotificationType.EINVOICE_PAYMENT_RECEIVED,
          title: 'Payment Received',
          message: `Payment received for invoice`,
          priority: NotificationPriority.HIGH,
          metadata: activityLog.details,
        }

      case ActionType.USER_COMPANY_ACCESS_GRANTED:
        return {
          type: NotificationType.USER_ADDED_TO_COMPANY,
          title: 'Added to Company',
          message: `You have been added to ${companyName}`,
          priority: NotificationPriority.HIGH,
        }

      case ActionType.USER_ROLE_CHANGED:
        return {
          type: NotificationType.USER_ROLE_CHANGED,
          title: 'Role Updated',
          message: `Your role has been updated in ${companyName}`,
          priority: NotificationPriority.NORMAL,
          metadata: activityLog.details,
        }

      case ActionType.SYSTEM_ERROR_OCCURRED:
        return {
          type: NotificationType.SECURITY_ALERT,
          title: 'System Alert',
          message: 'A system error has occurred',
          priority: NotificationPriority.URGENT,
          metadata: activityLog.details,
        }

      default:
        return null
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string, userId: string) {
    try {
      const notification = await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      })

      return notification
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      throw error
    }
  }

  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string,
    options: {
      limit?: number
      offset?: number
      unreadOnly?: boolean
    } = {}
  ) {
    try {
      const { limit = 20, offset = 0, unreadOnly = false } = options

      const notifications = await prisma.notification.findMany({
        where: {
          userId,
          ...(unreadOnly && { isRead: false }),
        },
        include: {
          activityLog: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
              company: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      })

      return notifications
    } catch (error) {
      console.error('Failed to get user notifications:', error)
      throw error
    }
  }

  /**
   * Get unread notification count for a user
   */
  static async getUnreadCount(userId: string) {
    try {
      const count = await prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      })

      return count
    } catch (error) {
      console.error('Failed to get unread count:', error)
      throw error
    }
  }
}
