import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/authOptions'
import { EinvoiceService } from '@/services/EinvoiceService'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const invoice = await EinvoiceService.getInvoiceById(id)

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: invoice.companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this invoice' },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(invoice)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()

    // Get current invoice to verify access
    const currentInvoice = await prisma.einvoice.findUnique({
      where: { id },
    })

    if (!currentInvoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: currentInvoice.companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this invoice' },
          { status: 403 }
        )
      }
    }

    const updateData = {
      id,
      ...body,
    }

    const updatedInvoice = await EinvoiceService.updateInvoice(updateData, session.user.id)

    return NextResponse.json(updatedInvoice)
  } catch (error) {
    console.error('Error updating invoice:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params

    // Get current invoice to verify access
    const currentInvoice = await prisma.einvoice.findUnique({
      where: { id },
    })

    if (!currentInvoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Verify user has access to this company
    if (session.user.role !== 'PORTAL_ADMIN') {
      const userAccess = await prisma.userCompanyAccess.findFirst({
        where: {
          userId: session.user.id,
          companyId: currentInvoice.companyId,
        },
      })

      if (!userAccess) {
        return NextResponse.json(
          { error: 'Access denied to this invoice' },
          { status: 403 }
        )
      }
    }

    await EinvoiceService.deleteInvoice(id, session.user.id)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
