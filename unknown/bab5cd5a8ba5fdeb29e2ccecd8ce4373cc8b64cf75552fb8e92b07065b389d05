import { PrismaClient, UserRole, CompanyStatus, NotificationType, NotificationPriority, ActionType, ActorType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create countries
  const countries = await Promise.all([
    prisma.country.upsert({
      where: { code: 'US' },
      update: {},
      create: {
        name: 'United States',
        code: 'US',
      },
    }),
    prisma.country.upsert({
      where: { code: 'CA' },
      update: {},
      create: {
        name: 'Canada',
        code: 'CA',
      },
    }),
    prisma.country.upsert({
      where: { code: 'UK' },
      update: {},
      create: {
        name: 'United Kingdom',
        code: 'UK',
      },
    }),
    prisma.country.upsert({
      where: { code: 'DE' },
      update: {},
      create: {
        name: 'Germany',
        code: 'DE',
      },
    }),
  ])

  console.log('✅ Countries created')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Portal Administrator',
      role: UserRole.PORTAL_ADMIN,
    },
  })

  console.log('✅ Admin user created')

  // Create regular user
  const userPassword = await bcrypt.hash('user123', 12)
  const regularUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'John Doe',
      role: UserRole.USER,
    },
  })

  console.log('✅ Regular user created')

  // Create companies
  const companies = await Promise.all([
    prisma.company.upsert({
      where: { id: 'company-1' },
      update: {},
      create: {
        id: 'company-1',
        name: 'Acme Corporation',
        status: CompanyStatus.ACTIVE,
        environment: 'production',
        subscriptionEnd: new Date('2024-12-31'),
        countryId: countries[0].id, // US
      },
    }),
    prisma.company.upsert({
      where: { id: 'company-2' },
      update: {},
      create: {
        id: 'company-2',
        name: 'Acme Corporation - Sandbox',
        status: CompanyStatus.ACTIVE,
        environment: 'sandbox',
        subscriptionEnd: new Date('2024-12-31'),
        countryId: countries[0].id, // US
      },
    }),
    prisma.company.upsert({
      where: { id: 'company-3' },
      update: {},
      create: {
        id: 'company-3',
        name: 'Global Tech Solutions',
        status: CompanyStatus.ACTIVE,
        environment: 'production',
        subscriptionEnd: new Date('2025-06-30'),
        countryId: countries[1].id, // Canada
      },
    }),
  ])

  console.log('✅ Companies created')

  // Create user company access
  await Promise.all([
    prisma.userCompanyAccess.upsert({
      where: {
        userId_companyId: {
          userId: regularUser.id,
          companyId: companies[0].id,
        },
      },
      update: {},
      create: {
        userId: regularUser.id,
        companyId: companies[0].id,
      },
    }),
    prisma.userCompanyAccess.upsert({
      where: {
        userId_companyId: {
          userId: regularUser.id,
          companyId: companies[1].id,
        },
      },
      update: {},
      create: {
        userId: regularUser.id,
        companyId: companies[1].id,
      },
    }),
  ])

  console.log('✅ User company access created')

  // Create sample invoices with enhanced structure
  const invoice1 = await prisma.einvoice.upsert({
    where: {
      companyId_invoiceNumber: {
        companyId: companies[0].id,
        invoiceNumber: 'INV-2024-0001',
      },
    },
    update: {},
    create: {
      invoiceNumber: 'INV-2024-0001',
      status: 'SENT',
      issueDate: new Date('2024-01-15'),
      dueDate: new Date('2024-02-15'),
      currency: 'USD',
      subtotal: 1500.00,
      taxAmount: 150.00,
      discountAmount: 0.00,
      totalAmount: 1650.00,
      billFromName: 'Acme Corporation',
      billFromAddress: {
        street: '123 Business Ave',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      billFromEmail: '<EMAIL>',
      billFromPhone: '******-0123',
      billToName: 'Tech Solutions Inc',
      billToAddress: {
        street: '456 Client St',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      billToEmail: '<EMAIL>',
      billToPhone: '******-0456',
      description: 'Software development services',
      notes: 'Payment due within 30 days',
      terms: 'Net 30 days',
      companyId: companies[0].id,
      createdById: regularUser.id,
      sentAt: new Date('2024-01-15'),
    },
  })

  const invoice2 = await prisma.einvoice.upsert({
    where: {
      companyId_invoiceNumber: {
        companyId: companies[0].id,
        invoiceNumber: 'INV-2024-0002',
      },
    },
    update: {},
    create: {
      invoiceNumber: 'INV-2024-0002',
      status: 'PAID',
      issueDate: new Date('2024-01-20'),
      dueDate: new Date('2024-02-20'),
      currency: 'USD',
      subtotal: 2500.00,
      taxAmount: 250.00,
      discountAmount: 100.00,
      totalAmount: 2650.00,
      billFromName: 'Acme Corporation',
      billFromAddress: {
        street: '123 Business Ave',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      billFromEmail: '<EMAIL>',
      billFromPhone: '******-0123',
      billToName: 'Global Enterprises',
      billToAddress: {
        street: '789 Enterprise Blvd',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'USA'
      },
      billToEmail: '<EMAIL>',
      description: 'Consulting services',
      notes: 'Thank you for your business',
      terms: 'Net 30 days',
      companyId: companies[0].id,
      createdById: regularUser.id,
      sentAt: new Date('2024-01-20'),
      paidAt: new Date('2024-02-10'),
    },
  })

  // Create invoice items
  await Promise.all([
    // Items for invoice 1
    prisma.einvoiceItem.create({
      data: {
        einvoiceId: invoice1.id,
        description: 'Frontend Development',
        quantity: 40,
        unitPrice: 25.00,
        discount: 0,
        taxRate: 10,
        lineTotal: 1100.00,
        unit: 'hours',
        productCode: 'DEV-FRONTEND',
      },
    }),
    prisma.einvoiceItem.create({
      data: {
        einvoiceId: invoice1.id,
        description: 'Backend Development',
        quantity: 20,
        unitPrice: 30.00,
        discount: 0,
        taxRate: 10,
        lineTotal: 660.00,
        unit: 'hours',
        productCode: 'DEV-BACKEND',
      },
    }),
    // Items for invoice 2
    prisma.einvoiceItem.create({
      data: {
        einvoiceId: invoice2.id,
        description: 'Strategic Consulting',
        quantity: 10,
        unitPrice: 200.00,
        discount: 100.00,
        taxRate: 10,
        lineTotal: 2090.00,
        unit: 'hours',
        productCode: 'CONSULT-STRATEGY',
      },
    }),
    prisma.einvoiceItem.create({
      data: {
        einvoiceId: invoice2.id,
        description: 'Technical Review',
        quantity: 5,
        unitPrice: 150.00,
        discount: 0,
        taxRate: 10,
        lineTotal: 825.00,
        unit: 'hours',
        productCode: 'REVIEW-TECH',
      },
    }),
  ])

  // Create invoice history
  await Promise.all([
    prisma.einvoiceHistory.create({
      data: {
        einvoiceId: invoice1.id,
        toStatus: 'DRAFT',
        changedById: regularUser.id,
        reason: 'Invoice created',
      },
    }),
    prisma.einvoiceHistory.create({
      data: {
        einvoiceId: invoice1.id,
        fromStatus: 'DRAFT',
        toStatus: 'SENT',
        changedById: regularUser.id,
        reason: 'Invoice sent to client',
      },
    }),
    prisma.einvoiceHistory.create({
      data: {
        einvoiceId: invoice2.id,
        toStatus: 'DRAFT',
        changedById: regularUser.id,
        reason: 'Invoice created',
      },
    }),
    prisma.einvoiceHistory.create({
      data: {
        einvoiceId: invoice2.id,
        fromStatus: 'DRAFT',
        toStatus: 'SENT',
        changedById: regularUser.id,
        reason: 'Invoice sent to client',
      },
    }),
    prisma.einvoiceHistory.create({
      data: {
        einvoiceId: invoice2.id,
        fromStatus: 'SENT',
        toStatus: 'PAID',
        changedById: regularUser.id,
        reason: 'Payment received',
        notes: 'Payment received via bank transfer',
      },
    }),
  ])

  console.log('✅ Sample invoices created')

  // Create user preferences
  await Promise.all([
    prisma.userPreference.upsert({
      where: { userId: adminUser.id },
      update: {},
      create: {
        userId: adminUser.id,
        receiveEmailNotifications: true,
        receiveInAppNotifications: true,
        receivePushNotifications: false,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
      },
    }),
    prisma.userPreference.upsert({
      where: { userId: regularUser.id },
      update: {},
      create: {
        userId: regularUser.id,
        receiveEmailNotifications: true,
        receiveInAppNotifications: true,
        receivePushNotifications: true,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
      },
    }),
  ])

  console.log('✅ User preferences created')

  // Create sample activity logs
  const activityLogs = await Promise.all([
    prisma.userActivityLog.create({
      data: {
        userId: regularUser.id,
        actorType: ActorType.USER,
        actorId: regularUser.id,
        companyId: companies[0].id,
        actionType: ActionType.EINVOICE_CREATED,
        targetType: 'Einvoice',
        targetId: invoice1.id,
        details: {
          invoiceNumber: 'INV-2024-0001',
          totalAmount: 1650.00,
          currency: 'USD',
          clientName: 'Tech Solutions Inc',
        },
        status: 'SUCCESS',
      },
    }),
    prisma.userActivityLog.create({
      data: {
        userId: regularUser.id,
        actorType: ActorType.USER,
        actorId: regularUser.id,
        companyId: companies[0].id,
        actionType: ActionType.EINVOICE_SENT,
        targetType: 'Einvoice',
        targetId: invoice1.id,
        details: {
          invoiceNumber: 'INV-2024-0001',
          totalAmount: 1650.00,
          clientEmail: '<EMAIL>',
        },
        status: 'SUCCESS',
      },
    }),
    prisma.userActivityLog.create({
      data: {
        userId: regularUser.id,
        actorType: ActorType.SYSTEM,
        companyId: companies[0].id,
        actionType: ActionType.EINVOICE_PAID,
        targetType: 'Einvoice',
        targetId: invoice2.id,
        details: {
          invoiceNumber: 'INV-2024-0002',
          totalAmount: 2650.00,
          paymentMethod: 'Bank Transfer',
          clientName: 'Global Enterprises',
        },
        status: 'SUCCESS',
      },
    }),
  ])

  console.log('✅ Sample activity logs created')

  // Create sample notifications
  await Promise.all([
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.EINVOICE_CREATED,
        title: 'Invoice Created Successfully',
        message: 'Your invoice INV-2024-0001 has been created and is ready for review.',
        priority: NotificationPriority.NORMAL,
        link: `/invoices/${invoice1.id}`,
        metadata: {
          invoiceNumber: 'INV-2024-0001',
          totalAmount: 1650.00,
          currency: 'USD',
          clientName: 'Tech Solutions Inc',
        },
        activityLogId: activityLogs[0].id,
      },
    }),
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.EINVOICE_STATUS_UPDATED,
        title: 'Invoice Sent',
        message: 'Invoice INV-2024-0001 has been sent to Tech Solutions Inc.',
        priority: NotificationPriority.NORMAL,
        link: `/invoices/${invoice1.id}`,
        metadata: {
          invoiceNumber: 'INV-2024-0001',
          status: 'SENT',
          clientEmail: '<EMAIL>',
        },
        activityLogId: activityLogs[1].id,
      },
    }),
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.EINVOICE_PAYMENT_RECEIVED,
        title: 'Payment Received',
        message: 'Payment of $2,650.00 has been received for invoice INV-2024-0002.',
        priority: NotificationPriority.HIGH,
        link: `/invoices/${invoice2.id}`,
        metadata: {
          invoiceNumber: 'INV-2024-0002',
          totalAmount: 2650.00,
          paymentMethod: 'Bank Transfer',
          clientName: 'Global Enterprises',
        },
        activityLogId: activityLogs[2].id,
      },
    }),
    prisma.notification.create({
      data: {
        userId: regularUser.id,
        type: NotificationType.USER_ADDED_TO_COMPANY,
        title: 'Welcome to Acme Corporation',
        message: 'You have been added to Acme Corporation. You can now access company resources.',
        priority: NotificationPriority.HIGH,
        link: '/companies/company-1',
        metadata: {
          companyName: 'Acme Corporation',
          role: 'USER',
        },
      },
    }),
    prisma.notification.create({
      data: {
        userId: adminUser.id,
        type: NotificationType.SYSTEM_ANNOUNCEMENT,
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur on Sunday at 2:00 AM UTC.',
        priority: NotificationPriority.NORMAL,
        metadata: {
          maintenanceDate: '2024-01-28T02:00:00Z',
          estimatedDuration: '2 hours',
        },
      },
    }),
  ])

  console.log('✅ Sample notifications created')

  console.log('🎉 Database seeding completed!')
  console.log('\n📋 Login credentials:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('User: <EMAIL> / user123')
  console.log('\n🔔 Features available:')
  console.log('- Real-time notifications')
  console.log('- Activity logging')
  console.log('- Multi-tenancy support')
  console.log('- Socket.io integration')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
