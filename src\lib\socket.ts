import { Server as NetServer } from 'http'
import { NextApiRequest, NextApiResponse } from 'next'
import { Server as ServerIO } from 'socket.io'
import { getServerSession } from 'next-auth'
import { authOptions } from './authOptions'
import { prisma } from './prisma'

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO
    }
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}

export default async function SocketHandler(
  req: NextApiRequest,
  res: NextApiResponseServerIO
) {
  if (!res.socket.server.io) {
    console.log('Setting up Socket.io server...')
    
    const io = new ServerIO(res.socket.server, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
      },
    })

    // Authentication middleware for Socket.io
    io.use(async (socket, next) => {
      try {
        const session = await getServerSession(authOptions)
        if (!session?.user) {
          return next(new Error('Authentication error'))
        }
        
        socket.data.userId = session.user.id
        socket.data.userRole = session.user.role
        next()
      } catch (error) {
        next(new Error('Authentication error'))
      }
    })

    io.on('connection', async (socket) => {
      const userId = socket.data.userId
      console.log(`User ${userId} connected with socket ${socket.id}`)

      // Store socket session in database
      try {
        await prisma.socketSession.create({
          data: {
            userId,
            socketId: socket.id,
            isActive: true,
          },
        })

        // Join user-specific room
        socket.join(`user_${userId}`)
        
        // Handle company context
        socket.on('join_company', async (companyId: string) => {
          // Verify user has access to this company
          const access = await prisma.userCompanyAccess.findFirst({
            where: {
              userId,
              companyId,
            },
          })

          if (access) {
            socket.join(`company_${companyId}`)
            
            // Update socket session with company context
            await prisma.socketSession.updateMany({
              where: {
                socketId: socket.id,
                isActive: true,
              },
              data: {
                companyId,
              },
            })
            
            socket.emit('company_joined', { companyId })
          } else {
            socket.emit('error', { message: 'Access denied to company' })
          }
        })

        socket.on('leave_company', async (companyId: string) => {
          socket.leave(`company_${companyId}`)
          
          // Remove company context from socket session
          await prisma.socketSession.updateMany({
            where: {
              socketId: socket.id,
              isActive: true,
            },
            data: {
              companyId: null,
            },
          })
          
          socket.emit('company_left', { companyId })
        })

        // Handle notification read status
        socket.on('mark_notification_read', async (notificationId: string) => {
          try {
            await prisma.notification.updateMany({
              where: {
                id: notificationId,
                userId,
              },
              data: {
                isRead: true,
                readAt: new Date(),
              },
            })
            
            socket.emit('notification_marked_read', { notificationId })
          } catch (error) {
            socket.emit('error', { message: 'Failed to mark notification as read' })
          }
        })

        socket.on('disconnect', async () => {
          console.log(`User ${userId} disconnected`)
          
          // Mark socket session as inactive
          await prisma.socketSession.updateMany({
            where: {
              socketId: socket.id,
              isActive: true,
            },
            data: {
              isActive: false,
            },
          })
        })

      } catch (error) {
        console.error('Socket connection error:', error)
        socket.disconnect()
      }
    })

    res.socket.server.io = io
  }

  res.end()
}

// Helper functions for emitting notifications
export async function emitNotificationToUser(
  io: ServerIO,
  userId: string,
  notification: any
) {
  io.to(`user_${userId}`).emit('new_notification', notification)
}

export async function emitNotificationToCompany(
  io: ServerIO,
  companyId: string,
  notification: any
) {
  io.to(`company_${companyId}`).emit('company_notification', notification)
}

export async function emitActivityLogToCompany(
  io: ServerIO,
  companyId: string,
  activityLog: any
) {
  io.to(`company_${companyId}`).emit('activity_update', activityLog)
}
