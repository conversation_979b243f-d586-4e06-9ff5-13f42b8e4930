'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export default function Home() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (status === 'unauthenticated') {
      router.push('/signin')
    } else if (status === 'authenticated') {
      // Check if user has selected a company
      const selectedCompany = sessionStorage.getItem('selectedCompany')
      if (selectedCompany) {
        router.push('/dashboard')
      } else {
        router.push('/select-country')
      }
    }
  }, [status, router])

  // Show loading spinner while determining where to redirect
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-lg text-gray-600">Loading Pinnacle Core Portal...</p>
      </div>
    </div>
  )
}
