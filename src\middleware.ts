import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // Allow access to auth pages when not authenticated
    if (!token && (pathname.startsWith('/signin') || pathname.startsWith('/signup'))) {
      return NextResponse.next()
    }

    // Redirect to signin if not authenticated and trying to access protected routes
    if (!token && !pathname.startsWith('/signin') && !pathname.startsWith('/signup')) {
      return NextResponse.redirect(new URL('/signin', req.url))
    }

    // Redirect authenticated users away from auth pages
    if (token && (pathname.startsWith('/signin') || pathname.startsWith('/signup'))) {
      return NextResponse.redirect(new URL('/select-country', req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // Allow access to auth pages
        if (pathname.startsWith('/signin') || pathname.startsWith('/signup')) {
          return true
        }
        
        // Require authentication for all other pages
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
