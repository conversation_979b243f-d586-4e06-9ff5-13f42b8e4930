import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Get the token from the request
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  })

  // Allow access to auth pages when not authenticated
  if (!token && (pathname.startsWith('/signin') || pathname.startsWith('/signup'))) {
    return NextResponse.next()
  }

  // Redirect to signin if not authenticated and trying to access protected routes
  if (!token && !pathname.startsWith('/signin') && !pathname.startsWith('/signup')) {
    return NextResponse.redirect(new URL('/signin', request.url))
  }

  // Redirect authenticated users away from auth pages
  if (token && (pathname.startsWith('/signin') || pathname.startsWith('/signup'))) {
    return NextResponse.redirect(new URL('/select-country', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
